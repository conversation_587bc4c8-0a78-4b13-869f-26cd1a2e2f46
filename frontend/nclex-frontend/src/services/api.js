// Serviço de API para comunicação com o backend
const API_BASE_URL = 'http://localhost:5000/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Obter token do localStorage
  getToken() {
    return localStorage.getItem('nclex_token');
  }

  // Salvar token no localStorage
  setToken(token) {
    localStorage.setItem('nclex_token', token);
  }

  // Remover token do localStorage
  removeToken() {
    localStorage.removeItem('nclex_token');
  }

  // Headers padrão para requisições
  getHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (includeAuth) {
      const token = this.getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  // Método genérico para fazer requisições
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(options.auth !== false),
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Erro na requisição');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // === AUTENTICAÇÃO ===

  // Registrar novo usuário
  async register(userData) {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
      auth: false,
    });
  }

  // Fazer login
  async login(credentials) {
    console.log('🌐 ApiService: Iniciando login request');
    const response = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
      auth: false,
    });

    console.log('🌐 ApiService: Login response recebida =', response);

    if (response.token) {
      console.log('🌐 ApiService: Salvando token no localStorage');
      this.setToken(response.token);
    } else {
      console.warn('⚠️ ApiService: Nenhum token recebido na resposta');
    }

    return response;
  }

  // Fazer logout
  async logout() {
    try {
      await this.request('/auth/logout', { method: 'POST' });
    } finally {
      this.removeToken();
    }
  }

  // Obter perfil do usuário
  async getProfile() {
    console.log('🌐 ApiService: Chamando getProfile');
    const response = await this.request('/auth/profile');
    console.log('🌐 ApiService: getProfile response =', response);
    return response;
  }

  // Atualizar perfil
  async updateProfile(profileData) {
    return this.request('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  // === QUESTÕES ===

  // Obter áreas de conteúdo e tipos de questões
  async getQuestionConfig() {
    return this.request('/questions/content-areas', { auth: false });
  }

  // Gerar questão
  async generateQuestion(params) {
    return this.request('/questions/generate', {
      method: 'POST',
      body: JSON.stringify(params),
      auth: false,
    });
  }

  // Traduzir questão
  async translateQuestion(questionText) {
    return this.request('/questions/translate', {
      method: 'POST',
      body: JSON.stringify({ questionText }),
    });
  }

  // Obter estatísticas de questões
  async getQuestionStats() {
    return this.request('/questions/stats');
  }

  // === SIMULADOS ===

  // Iniciar novo simulado
  async startSimulation() {
    return this.request('/simulations/start', {
      method: 'POST',
    });
  }

  // Obter próxima questão do simulado
  async getNextQuestion(simulationId) {
    return this.request(`/simulations/${simulationId}/next-question`);
  }

  // Submeter resposta
  async submitAnswer(simulationId, questionId, answer, timeSpent = 0) {
    return this.request(`/simulations/${simulationId}/questions/${questionId}/answer`, {
      method: 'POST',
      body: JSON.stringify({ answer, timeSpent }),
    });
  }

  // Obter histórico de simulados
  async getSimulationHistory() {
    return this.request('/simulations/history');
  }

  // Obter detalhes de um simulado
  async getSimulationDetails(simulationId) {
    return this.request(`/simulations/${simulationId}`);
  }

  // === ANÁLISES ===

  // Obter análise de performance do usuário
  async getUserPerformanceAnalysis() {
    return this.request('/analysis/performance');
  }

  // Obter análise detalhada de um simulado
  async getSimulationAnalysis(simulationId) {
    return this.request(`/analysis/simulation/${simulationId}`);
  }

  // Obter histórico de simulados com análise
  async getUserSimulationHistory(limit = 10, offset = 0) {
    return this.request(`/analysis/history?limit=${limit}&offset=${offset}`);
  }

  // Obter estatísticas por área de conteúdo
  async getContentAreaStatistics() {
    return this.request('/analysis/content-areas');
  }

  // Obter insights e recomendações personalizadas
  async getPersonalizedInsights() {
    return this.request('/analysis/insights');
  }

  // Obter comparação de performance
  async getPerformanceComparison() {
    return this.request('/analysis/comparison');
  }

  // === UTILITÁRIOS ===

  // Verificar se está autenticado
  isAuthenticated() {
    const token = this.getToken();
    console.log('🌐 ApiService: isAuthenticated - token =', token ? 'EXISTS' : 'NULL');
    return !!token;
  }

  // Verificar saúde da API
  async checkHealth() {
    return this.request('/health', { auth: false });
  }
}

export default new ApiService();

