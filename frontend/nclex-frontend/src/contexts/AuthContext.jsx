import React, { createContext, useContext, useState, useEffect } from 'react';
import apiService from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Verificar se o usuário está autenticado ao carregar a aplicação
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    console.log('🔍 AuthContext: Iniciando checkAuthStatus');
    try {
      const isAuth = apiService.isAuthenticated();
      console.log('🔍 AuthContext: isAuthenticated =', isAuth);

      if (isAuth) {
        console.log('🔍 AuthContext: Chamando getProfile...');
        const response = await apiService.getProfile();
        console.log('🔍 AuthContext: getProfile response =', response);
        setUser(response.user);
        console.log('🔍 AuthContext: User setado =', response.user);
      } else {
        console.log('🔍 AuthContext: Não autenticado, user permanece null');
      }
    } catch (error) {
      console.error('❌ AuthContext: Erro ao verificar autenticação:', error);
      apiService.removeToken();
      setUser(null);
    } finally {
      console.log('🔍 AuthContext: setLoading(false)');
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    console.log('🔐 AuthContext: Iniciando login com credentials =', credentials);
    try {
      setError(null);
      setLoading(true);
      console.log('🔐 AuthContext: setLoading(true)');

      console.log('🔐 AuthContext: Chamando apiService.login...');
      const response = await apiService.login(credentials);
      console.log('🔐 AuthContext: Login response =', response);

      setUser(response.user);
      console.log('🔐 AuthContext: User setado após login =', response.user);

      return response;
    } catch (error) {
      console.error('❌ AuthContext: Erro no login:', error);
      setError(error.message);
      throw error;
    } finally {
      console.log('🔐 AuthContext: setLoading(false) no finally do login');
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setError(null);
      setLoading(true);
      
      const response = await apiService.register(userData);
      
      // Após registro bem-sucedido, fazer login automaticamente
      if (response.token) {
        apiService.setToken(response.token);
        setUser(response.user);
      }
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      setUser(null);
      setError(null);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      setError(null);
      const response = await apiService.updateProfile(profileData);
      setUser(response.user);
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    clearError,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

