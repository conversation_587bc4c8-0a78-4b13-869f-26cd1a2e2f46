#!/usr/bin/env node

/**
 * Script para testar o sistema de logging expandido
 * Simula chamadas de API, cálculos TRI e fluxo de simulação
 */

const llmLogger = require('./src/utils/loggers/llmLogger');
const triLogger = require('./src/utils/loggers/triLogger');
const simulationLogger = require('./src/utils/loggers/simulationLogger');
const terminalLogger = require('./src/utils/loggers/terminalLogger');

async function testLogging() {
  console.log('🧪 Iniciando teste do sistema de logging...\n');

  // Simular inicialização do servidor
  terminalLogger.logServerStart(5000, 'development');

  // Simular criação de simulação
  const simulationId = 'sim_test_123';
  const userId = 'user_test_456';
  
  simulationLogger.logSimulationCreated(simulationId, userId);
  
  const initialState = {
    userId,
    currentAbility: 0.0,
    abilityHistory: [0.0],
    standardError: 1.0,
    questionCount: 0,
    correctAnswers: 0,
    responses: [],
    isComplete: false,
    result: null,
    confidence: 0.0
  };
  
  triLogger.logSimulationInit(userId, simulationId, initialState);
  simulationLogger.logSimulationStarted(simulationId, initialState);

  // Simular algumas questões e respostas
  for (let i = 1; i <= 5; i++) {
    console.log(`\n--- Questão ${i} ---`);
    
    // Simular seleção de questão
    const questionData = {
      id: `q_${i}`,
      difficulty: Math.random() * 2 - 1, // -1 a 1
      content_area: 'Safe and Effective Care Environment',
      question_type: 'multiple_choice'
    };
    
    triLogger.logQuestionSelection(simulationId, questionData, {
      currentAbility: initialState.currentAbility,
      targetDifficulty: questionData.difficulty,
      informationValue: Math.random(),
      method: 'MAXIMUM_INFORMATION'
    });
    
    simulationLogger.logQuestionPresented(simulationId, questionData);

    // Simular chamada LLM para gerar questão
    const callId = `llm_test_${i}_${Date.now()}`;
    const requestData = {
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'System prompt...' },
        { role: 'user', content: 'Generate question...' }
      ],
      temperature: 0.8,
      max_tokens: 1500
    };

    llmLogger.logLLMCallStart(callId, requestData);
    
    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    const responseData = {
      usage: {
        total_tokens: 150 + Math.floor(Math.random() * 100),
        prompt_tokens: 50,
        completion_tokens: 100
      },
      choices: [{
        message: { content: '{"question": "test"}' },
        finish_reason: 'stop'
      }]
    };
    
    const duration = 100 + Math.random() * 200;
    llmLogger.logLLMCallSuccess(callId, requestData, responseData, duration);
    
    terminalLogger.logLLMCall('gpt-4', 'QUESTION_GENERATION', duration, responseData.usage.total_tokens, true);

    // Simular resposta do usuário
    const isCorrect = Math.random() > 0.3; // 70% de chance de acerto
    
    triLogger.logUserResponse(simulationId, questionData.id, i, isCorrect);
    terminalLogger.logUserResponse(simulationId, i, isCorrect, questionData.difficulty);
    
    simulationLogger.logResponseSubmitted(simulationId, {
      questionId: questionData.id,
      selectedOption: ['A'],
      isCorrect
    });

    // Simular cálculo MLE
    const previousAbility = initialState.currentAbility;
    const newAbility = previousAbility + (isCorrect ? 0.1 : -0.1) + (Math.random() - 0.5) * 0.2;
    const standardError = Math.max(0.1, 1.0 - (i * 0.1));
    
    triLogger.logMLECalculation(simulationId, {
      previousAbility,
      newAbility,
      iterations: 5 + Math.floor(Math.random() * 10),
      convergence: true,
      standardError,
      responses: Array(i).fill({}),
      likelihood: Math.random()
    });

    // Atualizar estado
    initialState.currentAbility = newAbility;
    initialState.standardError = standardError;
    initialState.questionCount = i;
    if (isCorrect) initialState.correctAnswers++;
    initialState.confidence = Math.min(95, i * 15);

    terminalLogger.logTRICalculation(simulationId, newAbility, standardError, i);

    // Verificar critérios de terminação
    const shouldTerminate = i >= 5 || initialState.confidence >= 95;
    
    triLogger.logTerminationCheck(simulationId, {
      questionCount: i,
      currentAbility: newAbility,
      standardError,
      confidence: initialState.confidence,
      shouldTerminate,
      terminationReason: shouldTerminate ? (i >= 5 ? 'MAX_QUESTIONS_REACHED' : 'CONFIDENCE_THRESHOLD_MET') : null
    });

    if (shouldTerminate) {
      initialState.isComplete = true;
      initialState.result = newAbility > 0 ? 'pass' : 'fail';
      
      // Log decisão final
      triLogger.logPassFailDecision(simulationId, {
        finalAbility: newAbility,
        passingThreshold: 0.0,
        result: initialState.result,
        confidence: initialState.confidence,
        standardError,
        totalQuestions: i,
        correctAnswers: initialState.correctAnswers,
        accuracy: (initialState.correctAnswers / i) * 100,
        passProbability: newAbility > 0 ? initialState.confidence : (100 - initialState.confidence)
      });

      // Simular relatório TRI
      const report = {
        overview: {
          totalQuestions: i,
          correctAnswers: initialState.correctAnswers,
          accuracy: (initialState.correctAnswers / i) * 100,
          result: initialState.result,
          confidence: initialState.confidence
        },
        ability: {
          estimated: newAbility,
          percentile: 50 + newAbility * 20,
          standardError,
          passProbability: newAbility > 0 ? initialState.confidence : (100 - initialState.confidence)
        },
        progression: {
          abilityHistory: [0, ...Array(i).fill(0).map((_, idx) => (idx + 1) * 0.1)],
          questionDifficulties: Array(i).fill(0).map(() => Math.random() * 2 - 1),
          correctnessPattern: Array(i).fill(0).map(() => Math.random() > 0.3)
        }
      };

      triLogger.logTRIReport(simulationId, report);
      simulationLogger.logSimulationCompleted(simulationId, initialState);
      
      break;
    }

    // Simular atualização de estado TRI
    simulationLogger.logTRIStateUpdate(simulationId, 
      { currentAbility: previousAbility, standardError: 1.0 },
      { currentAbility: newAbility, standardError, confidence: initialState.confidence }
    );
  }

  // Finalizar sessão
  triLogger.logSimulationEnd(simulationId, initialState);
  simulationLogger.endSession(simulationId);

  // Exibir métricas finais
  console.log('\n📊 Métricas Finais:');
  terminalLogger.displayMetrics();

  // Simular algumas chamadas com erro
  console.log('\n🔥 Testando logs de erro...');

  const errorCallId = `llm_error_${Date.now()}`;
  const errorRequestData = {
    model: 'gpt-4',
    messages: [
      { role: 'system', content: 'System prompt...' },
      { role: 'user', content: 'Generate question...' }
    ]
  };
  llmLogger.logLLMCallStart(errorCallId, errorRequestData);
  
  const error = new Error('API rate limit exceeded');
  error.response = { status: 429, data: { error: { message: 'Rate limit exceeded' } } };
  
  llmLogger.logLLMCallError(errorCallId, errorRequestData, error, 1000);
  terminalLogger.logLLMCall('gpt-4', 'QUESTION_GENERATION', 1000, 0, false);
  terminalLogger.logError('LLM', 'GENERATE_QUESTION', error, { test: true });

  // Exibir métricas LLM
  console.log('\n🧠 Métricas LLM:');
  console.log(JSON.stringify(llmLogger.getMetrics(), null, 2));

  console.log('\n✅ Teste de logging concluído!');
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testLogging().catch(console.error);
}

module.exports = { testLogging };
