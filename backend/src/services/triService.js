// Serviço TRI (Teoria de Resposta ao Item) para adaptação de dificuldade
// Baseado nos princípios do NCLEX-RN CAT (Computer Adaptive Testing)

const triLogger = require('../utils/loggers/triLogger');
const terminalLogger = require('../utils/loggers/terminalLogger');

class TRIService {
  constructor() {
    // Parâmetros iniciais do TRI
    this.initialAbility = 0.0; // Habilidade inicial (logit)
    this.minAbility = -4.0;    // Habilidade mínima
    this.maxAbility = 4.0;     // Habilidade máxima
    this.minQuestions = 5;     // Mín<PERSON> de questões (simplificado para testes)
    this.maxQuestions = 10;    // Máximo de questões (simplificado para testes)
    this.confidenceLevel = 0.80; // Nível de confiança reduzido para facilitar testes
    this.passingStandard = 0.0;   // Padrão de aprovação (logit)
  }

  // Inicializar novo simulado TRI
  initializeSimulation(userId, simulationId = null) {
    const initialState = {
      userId,
      currentAbility: this.initialAbility,
      abilityHistory: [this.initialAbility],
      standardError: 1.0,
      questionCount: 0,
      correctAnswers: 0,
      responses: [],
      isComplete: false,
      result: null, // 'pass', 'fail', ou null
      confidence: 0.0
    };

    // Log inicialização da simulação TRI
    if (simulationId) {
      triLogger.logSimulationInit(userId, simulationId, initialState);
      terminalLogger.logTRICalculation(simulationId, this.initialAbility, 1.0, 0);
    }

    return initialState;
  }

  // Calcular próximo nível de dificuldade
  getNextDifficulty(simulation) {
    if (simulation.questionCount === 0) {
      // Primeira questão: dificuldade média
      return 0.0;
    }

    // Usar habilidade atual estimada como próxima dificuldade
    // Adicionar pequena variação para evitar questões idênticas
    const variation = (Math.random() - 0.5) * 0.5;
    const nextDifficulty = simulation.currentAbility + variation;

    // Limitar dentro dos bounds
    return Math.max(this.minAbility, Math.min(this.maxAbility, nextDifficulty));
  }

  // Atualizar estimativa de habilidade após resposta
  updateAbility(simulation, questionDifficulty, isCorrect, simulationId = null, questionId = null) {
    const previousAbility = simulation.currentAbility;
    const previousStandardError = simulation.standardError;

    const response = {
      questionNumber: simulation.questionCount + 1,
      difficulty: questionDifficulty,
      isCorrect,
      timestamp: new Date().toISOString()
    };

    simulation.responses.push(response);
    simulation.questionCount++;

    if (isCorrect) {
      simulation.correctAnswers++;
    }

    // Log resposta do usuário
    if (simulationId && questionId) {
      triLogger.logUserResponse(simulationId, questionId, response.questionNumber, isCorrect);
      terminalLogger.logUserResponse(simulationId, response.questionNumber, isCorrect, questionDifficulty);
    }

    // Calcular nova estimativa de habilidade usando Maximum Likelihood Estimation (MLE)
    const newAbility = this.calculateMLE(simulation.responses, simulationId);
    simulation.currentAbility = newAbility;
    simulation.abilityHistory.push(newAbility);

    // Calcular erro padrão
    simulation.standardError = this.calculateStandardError(simulation.responses, newAbility);

    // Log atualização do estado TRI
    if (simulationId) {
      terminalLogger.logTRICalculation(simulationId, newAbility, simulation.standardError, simulation.questionCount);
    }

    // Verificar se deve terminar o teste
    this.checkTerminationCriteria(simulation, simulationId);

    return simulation;
  }

  // Calcular Maximum Likelihood Estimation da habilidade
  calculateMLE(responses, simulationId = null) {
    if (responses.length === 0) return this.initialAbility;

    // Implementação simplificada do MLE para modelo 1PL (Rasch)
    let ability = this.initialAbility;
    const maxIterations = 20;
    const tolerance = 0.001;
    let iterations = 0;
    let convergence = false;

    for (let iter = 0; iter < maxIterations; iter++) {
      iterations = iter + 1;
      let numerator = 0;
      let denominator = 0;

      for (const response of responses) {
        const probability = this.itemResponseFunction(ability, response.difficulty);
        const weight = probability * (1 - probability);

        numerator += response.isCorrect ? (1 - probability) : -probability;
        denominator += weight;
      }

      if (denominator === 0) break;

      const delta = numerator / denominator;
      const previousAbility = ability;
      ability += delta;

      // Limitar dentro dos bounds
      ability = Math.max(this.minAbility, Math.min(this.maxAbility, ability));

      if (Math.abs(delta) < tolerance) {
        convergence = true;
        break;
      }
    }

    // Log cálculo MLE
    if (simulationId) {
      triLogger.logMLECalculation(simulationId, {
        previousAbility: responses.length > 1 ? this.initialAbility : ability,
        newAbility: ability,
        iterations,
        convergence,
        standardError: this.calculateStandardError(responses, ability),
        responses,
        likelihood: this.calculateLikelihood(responses, ability)
      });
    }

    return ability;
  }

  // Função de Resposta ao Item (modelo 1PL/Rasch)
  itemResponseFunction(ability, difficulty, discrimination = 1.0) {
    const exponent = discrimination * (ability - difficulty);
    const probability = 1 / (1 + Math.exp(-exponent));

    // Log função de resposta ao item (apenas em debug)
    if (process.env.LOG_LEVEL === 'debug') {
      triLogger.logItemResponseFunction(null, ability, difficulty, probability);
    }

    return probability;
  }

  // Calcular likelihood das respostas
  calculateLikelihood(responses, ability) {
    let likelihood = 1;

    for (const response of responses) {
      const probability = this.itemResponseFunction(ability, response.difficulty);
      likelihood *= response.isCorrect ? probability : (1 - probability);
    }

    return likelihood;
  }

  // Calcular erro padrão da estimativa de habilidade
  calculateStandardError(responses, ability) {
    if (responses.length === 0) return 1.0;

    let information = 0;

    for (const response of responses) {
      const probability = this.itemResponseFunction(ability, response.difficulty);
      information += probability * (1 - probability);
    }

    return information > 0 ? 1 / Math.sqrt(information) : 1.0;
  }

  // Verificar critérios de terminação do teste
  checkTerminationCriteria(simulation, simulationId = null) {
    const { questionCount, standardError, currentAbility } = simulation;
    const confidence = this.calculateConfidence(currentAbility, standardError);

    let shouldTerminate = false;
    let terminationReason = null;

    console.log(`🔍 Verificando critérios de terminação:`, {
      questionCount,
      minQuestions: this.minQuestions,
      maxQuestions: this.maxQuestions,
      currentAbility,
      standardError,
      confidenceLevel: this.confidenceLevel
    });

    // Critério 1: Número mínimo de questões
    if (questionCount < this.minQuestions) {
      console.log(`⏳ Ainda não atingiu mínimo: ${questionCount}/${this.minQuestions}`);
      shouldTerminate = false;
    }
    // Critério 2: Número máximo de questões
    else if (questionCount >= this.maxQuestions) {
      console.log(`🏁 Atingiu máximo de questões: ${questionCount}/${this.maxQuestions}`);
      simulation.isComplete = true;
      simulation.result = currentAbility >= this.passingStandard ? 'pass' : 'fail';
      simulation.confidence = confidence;
      shouldTerminate = true;
      terminationReason = 'MAX_QUESTIONS_REACHED';
    }
    // Critério 3: Confiança estatística
    else if (confidence >= this.confidenceLevel) {
      simulation.isComplete = true;
      simulation.result = currentAbility >= this.passingStandard ? 'pass' : 'fail';
      simulation.confidence = confidence;
      shouldTerminate = true;
      terminationReason = 'CONFIDENCE_THRESHOLD_MET';
    } else {
      console.log(`⏳ Continuando simulado - confiança insuficiente: ${confidence}`);
      simulation.confidence = confidence;
      shouldTerminate = false;
    }

    // Log critérios de terminação
    if (simulationId) {
      triLogger.logTerminationCheck(simulationId, {
        questionCount,
        currentAbility,
        standardError,
        confidence,
        shouldTerminate,
        terminationReason
      });

      // Log decisão de aprovação/reprovação se terminou
      if (shouldTerminate) {
        triLogger.logPassFailDecision(simulationId, {
          finalAbility: currentAbility,
          passingThreshold: this.passingStandard,
          result: simulation.result,
          confidence,
          standardError,
          totalQuestions: questionCount,
          correctAnswers: simulation.correctAnswers,
          accuracy: (simulation.correctAnswers / questionCount) * 100,
          passProbability: currentAbility >= this.passingStandard ? confidence : (1 - confidence)
        });
      }
    }

    return shouldTerminate;
  }

  // Calcular nível de confiança da decisão
  calculateConfidence(ability, standardError) {
    if (standardError === 0) return 1.0;

    // Z-score para a diferença entre habilidade e padrão de aprovação
    const zScore = Math.abs(ability - this.passingStandard) / standardError;
    
    // Converter para probabilidade usando distribuição normal
    return this.normalCDF(zScore);
  }

  // Função de distribuição cumulativa normal (aproximação)
  normalCDF(z) {
    // Aproximação de Abramowitz e Stegun
    const t = 1 / (1 + 0.2316419 * Math.abs(z));
    const d = 0.3989423 * Math.exp(-z * z / 2);
    const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    
    return z > 0 ? 1 - prob : prob;
  }

  // Obter estatísticas do simulado
  getSimulationStats(simulation) {
    const accuracy = simulation.questionCount > 0 ? 
      (simulation.correctAnswers / simulation.questionCount) * 100 : 0;

    return {
      questionCount: simulation.questionCount,
      correctAnswers: simulation.correctAnswers,
      accuracy: Math.round(accuracy * 100) / 100,
      currentAbility: Math.round(simulation.currentAbility * 1000) / 1000,
      standardError: Math.round(simulation.standardError * 1000) / 1000,
      confidence: Math.round(simulation.confidence * 10000) / 100,
      isComplete: simulation.isComplete,
      result: simulation.result,
      abilityRange: {
        min: Math.round((simulation.currentAbility - 1.96 * simulation.standardError) * 1000) / 1000,
        max: Math.round((simulation.currentAbility + 1.96 * simulation.standardError) * 1000) / 1000
      }
    };
  }

  // Converter dificuldade TRI para categoria legível
  getDifficultyCategory(difficulty) {
    if (difficulty < -1.0) return 'easy';
    if (difficulty > 1.0) return 'hard';
    return 'medium';
  }

  // Converter habilidade TRI para percentil aproximado
  abilityToPercentile(ability) {
    // Conversão aproximada baseada na distribuição normal
    const percentile = this.normalCDF(ability) * 100;
    return Math.round(percentile * 10) / 10;
  }

  // Predizer probabilidade de aprovação
  predictPassProbability(ability, standardError) {
    if (standardError === 0) {
      return ability >= this.passingStandard ? 100 : 0;
    }

    const zScore = (ability - this.passingStandard) / standardError;
    const probability = this.normalCDF(zScore) * 100;
    
    return Math.round(probability * 10) / 10;
  }

  // Gerar relatório detalhado do TRI
  generateTRIReport(simulation, simulationId = null) {
    const stats = this.getSimulationStats(simulation);
    const passProbability = this.predictPassProbability(
      simulation.currentAbility,
      simulation.standardError
    );

    const report = {
      overview: {
        totalQuestions: stats.questionCount,
        correctAnswers: stats.correctAnswers,
        accuracy: stats.accuracy,
        result: stats.result,
        confidence: stats.confidence
      },
      ability: {
        estimated: stats.currentAbility,
        percentile: this.abilityToPercentile(simulation.currentAbility),
        standardError: stats.standardError,
        confidenceInterval: stats.abilityRange,
        passProbability
      },
      progression: {
        abilityHistory: simulation.abilityHistory,
        questionDifficulties: simulation.responses.map(r => r.difficulty),
        correctnessPattern: simulation.responses.map(r => r.isCorrect)
      },
      interpretation: this.interpretResults(simulation)
    };

    // Log relatório TRI
    if (simulationId) {
      triLogger.logTRIReport(simulationId, report);
    }

    return report;
  }

  // Interpretar resultados para feedback ao usuário
  interpretResults(simulation) {
    const ability = simulation.currentAbility;
    const percentile = this.abilityToPercentile(ability);
    
    let interpretation = {
      level: '',
      description: '',
      recommendations: []
    };

    if (percentile >= 90) {
      interpretation.level = 'Excelente';
      interpretation.description = 'Demonstra conhecimento superior e alta probabilidade de aprovação no NCLEX.';
      interpretation.recommendations = [
        'Continue mantendo o alto nível de estudo',
        'Foque em questões de alta complexidade',
        'Revise áreas específicas com menor desempenho'
      ];
    } else if (percentile >= 70) {
      interpretation.level = 'Bom';
      interpretation.description = 'Demonstra conhecimento sólido com boa probabilidade de aprovação.';
      interpretation.recommendations = [
        'Continue estudando consistentemente',
        'Pratique mais questões de dificuldade média-alta',
        'Identifique e reforce áreas de fraqueza'
      ];
    } else if (percentile >= 50) {
      interpretation.level = 'Adequado';
      interpretation.description = 'Demonstra conhecimento básico, mas precisa de mais preparação.';
      interpretation.recommendations = [
        'Intensifique os estudos em áreas fundamentais',
        'Pratique mais questões de todos os níveis',
        'Considere recursos adicionais de estudo'
      ];
    } else {
      interpretation.level = 'Necessita Melhoria';
      interpretation.description = 'Indica necessidade de estudo adicional significativo.';
      interpretation.recommendations = [
        'Revise conceitos fundamentais de enfermagem',
        'Pratique questões básicas antes de avançar',
        'Considere orientação adicional ou curso preparatório'
      ];
    }

    return interpretation;
  }
}

module.exports = new TRIService();

