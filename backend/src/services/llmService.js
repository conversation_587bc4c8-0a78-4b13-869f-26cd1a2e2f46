const axios = require('axios');
const mockLLMService = require('./mockLLMService');
const llmLogger = require('../utils/loggers/llmLogger');
const terminalLogger = require('../utils/loggers/terminalLogger');

class LLMService {
  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY;
    this.apiBase = process.env.OPENAI_API_BASE || 'https://api.openai.com/v1';
    // Usar modelo apropriado baseado na API base
    if (this.apiBase.includes('openrouter.ai')) {
      this.model = 'z-ai/glm-4.5-air:free'; // Formato OpenRouter
    //  this.model = 'openai/gpt-4o-mini'; // Formato OpenRouter

    } else {
      this.model = 'gpt-4o-mini'; // Formato OpenAI padrão
    }
    // Usar LLM real se tiver chave de API válida, senão usar mock
    this.useMock = !this.apiKey || this.apiKey.trim() === '';

    console.log(`🔧 LLM Service configurado:`);
    console.log(`   - API Key: ${this.apiKey ? '✅ Configurada' : '❌ Não configurada'}`);
    console.log(`   - API Base: ${this.apiBase}`);
    console.log(`   - Modelo: ${this.model}`);
    console.log(`   - Modo: ${this.useMock ? '🎭 Mock Service' : '🤖 LLM Real'}`);
  }

  // Gerar questão NCLEX
  async generateQuestion(options = {}) {
    // Usar mock service se não tiver chave válida
    if (this.useMock) {
      console.log('🔧 Usando mock service para geração de questões');
      return await mockLLMService.generateQuestion(options);
    }

    // Código para API real da OpenAI
    const {
      difficulty = 'medium',
      contentArea = 'Safe and Effective Care Environment',
      questionType = 'multiple_choice',
      previousQuestions = []
    } = options;

    const callId = `llm_question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      const prompt = this.buildQuestionPrompt(difficulty, contentArea, questionType, previousQuestions);

      const requestData = {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.8,
        max_tokens: 1500,
        response_format: { type: 'json_object' }
      };

      // Log início da chamada LLM
      llmLogger.logLLMCallStart(callId, requestData);

      const response = await axios.post(
        `${this.apiBase}/chat/completions`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const duration = Date.now() - startTime;
      const questionData = JSON.parse(response.data.choices[0].message.content);
      const formattedQuestion = this.validateAndFormatQuestion(questionData);

      // Log sucesso da chamada LLM
      llmLogger.logLLMCallSuccess(callId, requestData, response.data, duration);

      // Log geração da questão
      llmLogger.logQuestionGeneration(formattedQuestion, {
        difficulty,
        contentArea,
        questionType,
        previousQuestionsCount: previousQuestions.length
      });

      // Log no terminal
      terminalLogger.logLLMCall(
        this.model,
        'QUESTION_GENERATION',
        duration,
        response.data.usage?.total_tokens || 0,
        true
      );

      return formattedQuestion;

    } catch (error) {
      const duration = Date.now() - startTime;

      // Log erro da chamada LLM
      llmLogger.logLLMCallError(callId, { model: this.model }, error, duration);

      // Log no terminal
      terminalLogger.logLLMCall(this.model, 'QUESTION_GENERATION', duration, 0, false);
      terminalLogger.logError('LLM', 'GENERATE_QUESTION', error, {
        difficulty,
        contentArea,
        questionType
      });

      console.error('Erro ao gerar questão:', error.response?.data || error.message);
      throw new Error('Falha ao gerar questão via LLM');
    }
  }

  // Prompt do sistema para o LLM
  getSystemPrompt() {
    return `Você é um especialista em enfermagem e criação de questões para o exame NCLEX-RN. 
    Sua tarefa é gerar questões de alta qualidade que simulem o exame real do NCLEX.

    INSTRUÇÕES IMPORTANTES:
    1. Todas as questões devem ser clinicamente precisas e baseadas em evidências
    2. Use terminologia médica apropriada
    3. Inclua cenários realistas de prática de enfermagem
    4. Forneça justificativas detalhadas para todas as opções
    5. Mantenha o nível de dificuldade solicitado
    6. Retorne SEMPRE um JSON válido no formato especificado

    ÁREAS DE CONTEÚDO NCLEX:
    - Safe and Effective Care Environment (Ambiente de Cuidado Seguro e Eficaz)
    - Health Promotion and Maintenance (Promoção e Manutenção da Saúde)
    - Psychosocial Integrity (Integridade Psicossocial)
    - Physiological Integrity (Integridade Fisiológica)

    TIPOS DE QUESTÕES:
    - multiple_choice: Uma resposta correta entre 4 opções
    - multiple_response: Múltiplas respostas corretas (selecionar todas que se aplicam)
    - fill_blank: Preencher lacuna com resposta numérica ou texto curto
    - drag_drop: Ordenar itens em sequência correta

    NÍVEIS DE DIFICULDADE:
    - easy: Conhecimento básico, recall direto
    - medium: Aplicação de conhecimento, análise simples
    - hard: Síntese, avaliação, cenários complexos`;
  }

  // Construir prompt específico para a questão
  buildQuestionPrompt(difficulty, contentArea, questionType, previousQuestions) {
    let prompt = `Gere uma questão NCLEX-RN com as seguintes especificações:

ESPECIFICAÇÕES:
- Nível de dificuldade: ${difficulty}
- Área de conteúdo: ${contentArea}
- Tipo de questão: ${questionType}
- Idioma: Inglês (questão original em inglês)

FORMATO DE RESPOSTA (JSON):
{
  "question_text": "Texto da questão com cenário clínico detalhado",
  "question_type": "${questionType}",
  "content_area": "${contentArea}",
  "difficulty_level": "${difficulty}",
  "options": [
    {"id": "A", "text": "Opção A"},
    {"id": "B", "text": "Opção B"},
    {"id": "C", "text": "Opção C"},
    {"id": "D", "text": "Opção D"}
  ],
  "correct_answer": ["A"], // Array com IDs das respostas corretas
  "rationale": {
    "correct": "Explicação detalhada da resposta correta",
    "incorrect": {
      "B": "Por que a opção B está incorreta",
      "C": "Por que a opção C está incorreta", 
      "D": "Por que a opção D está incorreta"
    }
  },
  "nursing_process": "Assessment|Diagnosis|Planning|Implementation|Evaluation",
  "client_needs": "Subcategoria específica da área de conteúdo",
  "cognitive_level": "Knowledge|Comprehension|Application|Analysis|Synthesis|Evaluation"
}`;

    // Adicionar contexto de questões anteriores para evitar repetição
    if (previousQuestions.length > 0) {
      prompt += `\n\nEVITE REPETIR TEMAS DAS QUESTÕES ANTERIORES:
${previousQuestions.map((q, i) => `${i + 1}. ${q.substring(0, 100)}...`).join('\n')}`;
    }

    // Ajustar prompt baseado no tipo de questão
    switch (questionType) {
      case 'multiple_response':
        prompt += `\n\nPARA MULTIPLE RESPONSE:
- Inclua 5-6 opções
- 2-3 respostas devem estar corretas
- Use "Select all that apply" na questão`;
        break;
      
      case 'fill_blank':
        prompt += `\n\nPARA FILL BLANK:
- Questão deve ter uma lacuna para preenchimento
- Resposta deve ser numérica (dosagem, tempo, etc.) ou texto curto
- Inclua unidades de medida quando apropriado`;
        break;
      
      case 'drag_drop':
        prompt += `\n\nPARA DRAG AND DROP:
- Forneça 4-6 itens para ordenar
- Sequência deve ser lógica (prioridade, tempo, processo)
- Explique a ordem correta na justificativa`;
        break;
    }

    return prompt;
  }

  // Validar e formatar questão gerada
  validateAndFormatQuestion(questionData) {
    // Validações básicas
    if (!questionData.question_text || !questionData.options || !questionData.correct_answer) {
      throw new Error('Questão gerada está incompleta');
    }

    // Garantir que correct_answer seja array
    if (!Array.isArray(questionData.correct_answer)) {
      questionData.correct_answer = [questionData.correct_answer];
    }

    // Adicionar metadados
    questionData.generated_at = new Date().toISOString();
    questionData.llm_model = this.model;

    return questionData;
  }

  // Gerar justificativa adicional para uma questão
  async generateRationale(questionText, correctAnswer, incorrectAnswers) {
    const callId = `llm_rationale_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const startTime = Date.now();

    try {
      const prompt = `Forneça uma justificativa detalhada para esta questão NCLEX:

QUESTÃO: ${questionText}
RESPOSTA CORRETA: ${correctAnswer}
RESPOSTAS INCORRETAS: ${incorrectAnswers.join(', ')}

Explique:
1. Por que a resposta correta está certa (base científica, guidelines, evidências)
2. Por que cada resposta incorreta está errada
3. Conceitos de enfermagem relevantes
4. Implicações para a prática clínica

Formato JSON:
{
  "detailed_rationale": "Explicação completa",
  "key_concepts": ["conceito1", "conceito2"],
  "clinical_implications": "Implicações práticas",
  "references": ["referência1", "referência2"]
}`;

      const requestData = {
        model: this.model,
        messages: [
          { role: 'system', content: this.getSystemPrompt() },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 800,
        response_format: { type: 'json_object' }
      };

      // Log início da chamada LLM
      llmLogger.logLLMCallStart(callId, requestData);

      const response = await axios.post(
        `${this.apiBase}/chat/completions`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const duration = Date.now() - startTime;
      const rationaleData = JSON.parse(response.data.choices[0].message.content);

      // Log sucesso da chamada LLM
      llmLogger.logLLMCallSuccess(callId, requestData, response.data, duration);

      // Log geração da justificativa
      llmLogger.logRationaleGeneration(rationaleData, {
        questionLength: questionText.length,
        correctAnswer,
        incorrectAnswersCount: incorrectAnswers.length
      });

      // Log no terminal
      terminalLogger.logLLMCall(
        this.model,
        'RATIONALE_GENERATION',
        duration,
        response.data.usage?.total_tokens || 0,
        true
      );

      return rationaleData;

    } catch (error) {
      const duration = Date.now() - startTime;

      // Log erro da chamada LLM
      llmLogger.logLLMCallError(callId, { model: this.model }, error, duration);

      // Log no terminal
      terminalLogger.logLLMCall(this.model, 'RATIONALE_GENERATION', duration, 0, false);
      terminalLogger.logError('LLM', 'GENERATE_RATIONALE', error, {
        questionText: questionText.substring(0, 100) + '...'
      });

      console.error('Erro ao gerar justificativa:', error);
      return null;
    }
  }

  // Traduzir questão para português
  async translateQuestion(questionText) {
    // Usar mock service se não tiver chave válida
    if (this.useMock) {
      console.log('🔧 Usando mock service para tradução');
      return await mockLLMService.translateQuestion(questionText);
    }
    try {
      const prompt = `Traduza esta questão NCLEX do inglês para o português brasileiro, mantendo:
1. Terminologia médica precisa
2. Contexto clínico apropriado
3. Clareza e profissionalismo

QUESTÃO EM INGLÊS:
${questionText}

Retorne apenas a tradução em português, sem explicações adicionais.`;

      const response = await axios.post(
        `${this.apiBase}/chat/completions`,
        {
          model: this.model,
          messages: [
            { 
              role: 'system', 
              content: 'Você é um tradutor médico especializado em enfermagem. Traduza com precisão técnica.' 
            },
            { role: 'user', content: prompt }
          ],
          temperature: 0.2,
          max_tokens: 500
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data.choices[0].message.content.trim();

    } catch (error) {
      console.error('Erro ao traduzir questão:', error);
      throw new Error('Falha ao traduzir questão');
    }
  }
}

module.exports = new LLMService();

