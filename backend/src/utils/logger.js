const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// Configuração de cores para diferentes níveis
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
  verbose: 'cyan'
};

winston.addColors(colors);

// Formato personalizado para logs estruturados
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Formato para console (desenvolvimento)
const consoleFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'HH:mm:ss.SSS'
  }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, service, action, duration, ...meta }) => {
    let logMessage = `${timestamp} [${level}]`;
    
    if (service) logMessage += ` [${service}]`;
    if (action) logMessage += ` ${action}`;
    if (duration) logMessage += ` (${duration}ms)`;
    
    logMessage += `: ${message}`;
    
    // Adicionar metadata se existir
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// Configuração de transports
const transports = [
  // Console para desenvolvimento
  new winston.transports.Console({
    level: process.env.LOG_LEVEL || 'info',
    format: consoleFormat,
    handleExceptions: true
  }),

  // Arquivo geral com rotação diária
  new DailyRotateFile({
    filename: path.join(process.cwd(), 'logs', 'application-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '14d',
    format: logFormat,
    level: 'info'
  }),

  // Arquivo de erros
  new DailyRotateFile({
    filename: path.join(process.cwd(), 'logs', 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '30d',
    format: logFormat,
    level: 'error'
  }),

  // Arquivo de debug (apenas em desenvolvimento)
  ...(process.env.NODE_ENV === 'development' ? [
    new DailyRotateFile({
      filename: path.join(process.cwd(), 'logs', 'debug-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '50m',
      maxFiles: '7d',
      format: logFormat,
      level: 'debug'
    })
  ] : [])
];

// Criar logger principal
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports,
  exitOnError: false
});

// Classe para logging estruturado
class StructuredLogger {
  constructor(service) {
    this.service = service;
    this.startTimes = new Map();
  }

  // Iniciar timer para medir duração
  startTimer(actionId) {
    this.startTimes.set(actionId, Date.now());
  }

  // Finalizar timer e obter duração
  endTimer(actionId) {
    const startTime = this.startTimes.get(actionId);
    if (startTime) {
      this.startTimes.delete(actionId);
      return Date.now() - startTime;
    }
    return null;
  }

  // Log com contexto estruturado
  log(level, message, action = null, metadata = {}) {
    const logData = {
      message,
      service: this.service,
      ...metadata
    };

    if (action) {
      logData.action = action;
    }

    logger.log(level, logData);
  }

  // Métodos de conveniência
  debug(message, action, metadata = {}) {
    this.log('debug', message, action, metadata);
  }

  info(message, action, metadata = {}) {
    this.log('info', message, action, metadata);
  }

  warn(message, action, metadata = {}) {
    this.log('warn', message, action, metadata);
  }

  error(message, action, metadata = {}) {
    this.log('error', message, action, metadata);
  }

  // Log com duração automática
  logWithDuration(level, message, action, actionId, metadata = {}) {
    const duration = this.endTimer(actionId);
    this.log(level, message, action, { ...metadata, duration });
  }

  // Log de início de operação
  logStart(message, action, actionId, metadata = {}) {
    this.startTimer(actionId);
    this.log('debug', `${message} - INICIADO`, action, { ...metadata, actionId });
  }

  // Log de fim de operação
  logEnd(message, action, actionId, metadata = {}) {
    const duration = this.endTimer(actionId);
    this.log('info', `${message} - CONCLUÍDO`, action, { ...metadata, actionId, duration });
  }

  // Log de erro com contexto
  logError(error, action, metadata = {}) {
    this.log('error', error.message, action, {
      ...metadata,
      stack: error.stack,
      errorType: error.constructor.name
    });
  }
}

// Factory para criar loggers específicos
function createLogger(service) {
  return new StructuredLogger(service);
}

// Middleware para logging de requests HTTP
function createRequestLogger() {
  return (req, res, next) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();
    
    req.requestId = requestId;
    req.startTime = startTime;

    // Log do request
    logger.info({
      message: 'HTTP Request',
      service: 'HTTP',
      action: 'REQUEST',
      requestId,
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id
    });

    // Interceptar response
    const originalSend = res.send;
    res.send = function(data) {
      const duration = Date.now() - startTime;
      
      logger.info({
        message: 'HTTP Response',
        service: 'HTTP',
        action: 'RESPONSE',
        requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration,
        responseSize: Buffer.byteLength(data || '', 'utf8')
      });

      originalSend.call(this, data);
    };

    next();
  };
}

// Função para criar diretório de logs se não existir
function ensureLogDirectory() {
  const fs = require('fs');
  const logDir = path.join(process.cwd(), 'logs');
  
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}

// Inicializar diretório de logs
ensureLogDirectory();

module.exports = {
  logger,
  createLogger,
  createRequestLogger,
  StructuredLogger
};
