const chalk = require('chalk');
const Table = require('cli-table3');
const { createLogger } = require('../logger');

class TerminalLogger {
  constructor() {
    this.logger = createLogger('TERMINAL');
    this.isEnabled = process.env.NODE_ENV === 'development';
    this.metrics = {
      requests: 0,
      llmCalls: 0,
      triCalculations: 0,
      errors: 0,
      startTime: Date.now()
    };
  }

  // Configurar cores e símbolos
  getSymbols() {
    return {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      debug: '◦',
      arrow: '→',
      bullet: '•',
      timer: '⏱',
      api: '🔗',
      brain: '🧠',
      chart: '📊',
      user: '👤',
      question: '❓',
      check: '✅',
      cross: '❌'
    };
  }

  // Log de inicialização do servidor
  logServerStart(port, environment) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    console.log('\n🚀 NCLEX Simulator - Backend Iniciado');
    console.log('━'.repeat(50));
    console.log(`${symbols.info} Porta: ${port}`);
    console.log(`${symbols.info} Ambiente: ${environment}`);
    console.log(`${symbols.info} Timestamp: ${new Date().toISOString()}`);
    console.log(`${symbols.info} Logging: ATIVO`);
    console.log('━'.repeat(50) + '\n');
  }

  // Log de request HTTP
  logHTTPRequest(method, url, statusCode, duration, userId = null) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    this.metrics.requests++;

    const userInfo = userId ? `[User:${userId}]` : '';

    console.log(
      `${symbols.api} ${method.padEnd(6)} ${url.padEnd(30)} ` +
      `${statusCode} ${duration}ms ${userInfo}`
    );
  }

  // Log de chamada LLM
  logLLMCall(model, requestType, duration, tokensUsed, success) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    this.metrics.llmCalls++;

    const statusSymbol = success ? symbols.success : symbols.error;

    console.log(
      `${symbols.brain} ${statusSymbol} LLM ${model} ${requestType} ` +
      `${duration}ms ${tokensUsed} tokens`
    );
  }

  // Log de cálculo TRI
  logTRICalculation(simulationId, ability, standardError, questionCount) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    this.metrics.triCalculations++;

    const confidenceLevel = this.calculateConfidenceLevel(standardError);

    console.log(
      `${symbols.chart} TRI [${simulationId}] ` +
      `Habilidade: ${ability.toFixed(3)} ` +
      `SE: ${standardError.toFixed(3)} ` +
      `Q: ${questionCount} ` +
      `Conf: ${confidenceLevel}%`
    );
  }

  // Log de resposta do usuário
  logUserResponse(simulationId, questionNumber, isCorrect, difficulty) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    const resultSymbol = isCorrect ? symbols.check : symbols.cross;

    console.log(
      `${symbols.user} ${resultSymbol} [${simulationId}] ` +
      `Q${questionNumber} ${isCorrect ? 'CORRETO' : 'INCORRETO'} ` +
      `Dif: ${difficulty.toFixed(2)}`
    );
  }

  // Log de erro
  logError(service, action, error, context = {}) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    this.metrics.errors++;

    console.log(
      `${symbols.error} ERRO [${service}] ` +
      `${action}: ${error.message}`
    );

    if (Object.keys(context).length > 0) {
      console.log('  Contexto:', JSON.stringify(context, null, 2));
    }
  }

  // Log de warning
  logWarning(service, message, details = {}) {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    console.log(
      `${symbols.warning} AVISO [${service}] ${message}`
    );

    if (Object.keys(details).length > 0) {
      console.log('  Detalhes:', JSON.stringify(details, null, 2));
    }
  }

  // Exibir métricas em tempo real
  displayMetrics() {
    if (!this.isEnabled) return;

    const symbols = this.getSymbols();
    const uptime = Date.now() - this.metrics.startTime;
    const uptimeFormatted = this.formatDuration(uptime);

    console.log('\n📊 Métricas em Tempo Real');
    console.log('━'.repeat(40));
    console.log(`Uptime: ${uptimeFormatted}`);
    console.log(`Requests HTTP: ${this.metrics.requests}`);
    console.log(`Chamadas LLM: ${this.metrics.llmCalls}`);
    console.log(`Cálculos TRI: ${this.metrics.triCalculations}`);
    console.log(`Erros: ${this.metrics.errors}`);
    console.log(`Req/min: ${Math.round((this.metrics.requests / (uptime / 60000)) * 100) / 100}`);
    console.log('━'.repeat(40) + '\n');
  }

  // Exibir dashboard de simulações ativas
  displayActiveSimulations(simulations) {
    if (!this.isEnabled || !simulations.length) return;

    console.log('\n🎯 Simulações Ativas');
    console.log('━'.repeat(60));

    simulations.forEach(sim => {
      console.log(`ID: ${sim.id} | Usuário: ${sim.userId} | Questões: ${sim.questionCount} | Habilidade: ${sim.ability.toFixed(3)} | Status: ${sim.isComplete ? 'COMPLETO' : 'ATIVO'}`);
    });

    console.log('━'.repeat(60) + '\n');
  }

  // Helpers para cores (simplificados)
  getMethodColor(method) {
    return method; // Retorna o método sem cor
  }

  getStatusColor(status) {
    return status; // Retorna o status sem cor
  }

  getDifficultyColor(difficulty) {
    return difficulty; // Retorna a dificuldade sem cor
  }

  getConfidenceColor(confidence) {
    return confidence; // Retorna a confiança sem cor
  }

  // Calcular nível de confiança baseado no erro padrão
  calculateConfidenceLevel(standardError) {
    // Aproximação: confiança inversamente proporcional ao erro padrão
    const confidence = Math.max(0, Math.min(100, (1 - standardError) * 100));
    return Math.round(confidence);
  }

  // Formatar duração
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  // Limpar terminal
  clear() {
    if (this.isEnabled) {
      console.clear();
    }
  }

  // Separador visual
  separator(title = '') {
    if (!this.isEnabled) return;

    const line = '─'.repeat(50);
    if (title) {
      console.log(`\n${line} ${title} ${line}\n`);
    } else {
      console.log(`\n${line}\n`);
    }
  }

  // Habilitar/desabilitar logging
  setEnabled(enabled) {
    this.isEnabled = enabled;
    this.logger.info(`Terminal logging ${enabled ? 'habilitado' : 'desabilitado'}`, 'TOGGLE_LOGGING');
  }
}

// Instância singleton
const terminalLogger = new TerminalLogger();

module.exports = terminalLogger;
