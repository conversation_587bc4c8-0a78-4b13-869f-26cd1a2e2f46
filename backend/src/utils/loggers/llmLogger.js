const { createLogger } = require('../logger');

class LLMLogger {
  constructor() {
    this.logger = createLogger('LLM');
    this.metrics = {
      totalCalls: 0,
      totalTokens: 0,
      totalCost: 0,
      averageLatency: 0,
      successRate: 0,
      errors: 0
    };
    this.callHistory = [];
  }

  // Log de chamada para LLM - início
  logLLMCallStart(callId, requestData) {
    this.logger.logStart('Chamada LLM', 'LLM_CALL', callId, {
      model: requestData.model,
      promptLength: requestData.messages?.reduce((acc, msg) => acc + msg.content.length, 0) || 0,
      temperature: requestData.temperature,
      maxTokens: requestData.max_tokens,
      requestType: this.inferRequestType(requestData)
    });
  }

  // Log de chamada para LLM - sucesso
  logLLMCallSuccess(callId, requestData, responseData, duration) {
    const tokensUsed = responseData.usage?.total_tokens || 0;
    const estimatedCost = this.calculateCost(requestData.model, tokensUsed);
    
    this.logger.logEnd('Chamada LLM', 'LLM_CALL', callId, {
      model: requestData.model,
      tokensUsed,
      promptTokens: responseData.usage?.prompt_tokens || 0,
      completionTokens: responseData.usage?.completion_tokens || 0,
      estimatedCost,
      responseLength: responseData.choices?.[0]?.message?.content?.length || 0,
      finishReason: responseData.choices?.[0]?.finish_reason,
      requestType: this.inferRequestType(requestData)
    });

    // Atualizar métricas
    this.updateMetrics(true, duration, tokensUsed, estimatedCost);
    
    // Salvar no histórico
    this.saveToHistory(callId, requestData, responseData, duration, true);
  }

  // Log de chamada para LLM - erro
  logLLMCallError(callId, requestData, error, duration) {
    this.logger.logError(error, 'LLM_CALL_ERROR', {
      callId,
      model: requestData.model,
      requestType: this.inferRequestType(requestData),
      errorCode: error.response?.status,
      errorMessage: error.response?.data?.error?.message || error.message,
      duration
    });

    // Atualizar métricas
    this.updateMetrics(false, duration, 0, 0);
    
    // Salvar no histórico
    this.saveToHistory(callId, requestData, null, duration, false, error);
  }

  // Log de geração de questão
  logQuestionGeneration(questionData, metadata = {}) {
    this.logger.info('Questão gerada pelo LLM', 'QUESTION_GENERATED', {
      difficulty: questionData.difficulty,
      contentArea: questionData.content_area,
      questionType: questionData.question_type,
      hasRationale: !!questionData.rationale,
      optionsCount: questionData.options?.length || 0,
      ...metadata
    });
  }

  // Log de geração de justificativa
  logRationaleGeneration(rationaleData, metadata = {}) {
    this.logger.info('Justificativa gerada pelo LLM', 'RATIONALE_GENERATED', {
      hasDetailedRationale: !!rationaleData.detailed_rationale,
      keyConceptsCount: rationaleData.key_concepts?.length || 0,
      hasReferences: !!rationaleData.references,
      ...metadata
    });
  }

  // Inferir tipo de request baseado no conteúdo
  inferRequestType(requestData) {
    const content = requestData.messages?.[1]?.content?.toLowerCase() || '';
    
    if (content.includes('gere uma questão') || content.includes('generate a question')) {
      return 'QUESTION_GENERATION';
    } else if (content.includes('justificativa') || content.includes('rationale')) {
      return 'RATIONALE_GENERATION';
    } else if (content.includes('análise') || content.includes('analysis')) {
      return 'ANALYSIS';
    }
    
    return 'UNKNOWN';
  }

  // Calcular custo estimado baseado no modelo e tokens
  calculateCost(model, tokens) {
    // Preços aproximados por 1K tokens (valores de exemplo - ajustar conforme API)
    const pricing = {
      'gpt-4': 0.03,
      'gpt-4-turbo': 0.01,
      'gpt-3.5-turbo': 0.002,
      'claude-3-opus': 0.015,
      'claude-3-sonnet': 0.003,
      'claude-3-haiku': 0.00025
    };

    const pricePerK = pricing[model] || 0.01; // Default
    return (tokens / 1000) * pricePerK;
  }

  // Atualizar métricas internas
  updateMetrics(success, duration, tokens, cost) {
    this.metrics.totalCalls++;
    this.metrics.totalTokens += tokens;
    this.metrics.totalCost += cost;
    
    if (!success) {
      this.metrics.errors++;
    }

    // Calcular média de latência
    const totalDuration = (this.metrics.averageLatency * (this.metrics.totalCalls - 1)) + duration;
    this.metrics.averageLatency = totalDuration / this.metrics.totalCalls;

    // Calcular taxa de sucesso
    this.metrics.successRate = ((this.metrics.totalCalls - this.metrics.errors) / this.metrics.totalCalls) * 100;
  }

  // Salvar chamada no histórico
  saveToHistory(callId, requestData, responseData, duration, success, error = null) {
    const historyEntry = {
      callId,
      timestamp: new Date().toISOString(),
      model: requestData.model,
      requestType: this.inferRequestType(requestData),
      duration,
      success,
      tokensUsed: responseData?.usage?.total_tokens || 0,
      estimatedCost: this.calculateCost(requestData.model, responseData?.usage?.total_tokens || 0),
      error: error ? {
        message: error.message,
        code: error.response?.status,
        type: error.constructor.name
      } : null
    };

    this.callHistory.push(historyEntry);

    // Manter apenas os últimos 1000 registros
    if (this.callHistory.length > 1000) {
      this.callHistory = this.callHistory.slice(-1000);
    }
  }

  // Obter métricas atuais
  getMetrics() {
    return {
      ...this.metrics,
      recentCalls: this.callHistory.slice(-10),
      timestamp: new Date().toISOString()
    };
  }

  // Log de métricas periódicas
  logMetrics() {
    this.logger.info('Métricas LLM', 'METRICS_REPORT', this.getMetrics());
  }

  // Resetar métricas
  resetMetrics() {
    this.metrics = {
      totalCalls: 0,
      totalTokens: 0,
      totalCost: 0,
      averageLatency: 0,
      successRate: 0,
      errors: 0
    };
    this.callHistory = [];
    
    this.logger.info('Métricas LLM resetadas', 'METRICS_RESET');
  }

  // Log de análise de performance
  logPerformanceAnalysis() {
    const recentCalls = this.callHistory.slice(-100);
    const avgLatency = recentCalls.reduce((acc, call) => acc + call.duration, 0) / recentCalls.length;
    const errorRate = (recentCalls.filter(call => !call.success).length / recentCalls.length) * 100;
    
    this.logger.info('Análise de Performance LLM', 'PERFORMANCE_ANALYSIS', {
      recentCallsCount: recentCalls.length,
      averageLatency: Math.round(avgLatency),
      errorRate: Math.round(errorRate * 100) / 100,
      totalCostToday: this.calculateDailyCost(),
      mostUsedModel: this.getMostUsedModel(),
      requestTypeDistribution: this.getRequestTypeDistribution()
    });
  }

  // Calcular custo diário
  calculateDailyCost() {
    const today = new Date().toDateString();
    return this.callHistory
      .filter(call => new Date(call.timestamp).toDateString() === today)
      .reduce((acc, call) => acc + call.estimatedCost, 0);
  }

  // Obter modelo mais usado
  getMostUsedModel() {
    const modelCounts = {};
    this.callHistory.forEach(call => {
      modelCounts[call.model] = (modelCounts[call.model] || 0) + 1;
    });
    
    return Object.entries(modelCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
  }

  // Obter distribuição de tipos de request
  getRequestTypeDistribution() {
    const typeCounts = {};
    this.callHistory.forEach(call => {
      typeCounts[call.requestType] = (typeCounts[call.requestType] || 0) + 1;
    });
    
    return typeCounts;
  }
}

// Instância singleton
const llmLogger = new LLMLogger();

module.exports = llmLogger;
