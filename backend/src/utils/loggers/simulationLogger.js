const { createLogger } = require('../logger');

class SimulationLogger {
  constructor() {
    this.logger = createLogger('SIMULATION');
    this.activeSessions = new Map();
  }

  // Log de criação de nova simulação
  logSimulationCreated(simulationId, userId, metadata = {}) {
    this.activeSessions.set(simulationId, {
      userId,
      startTime: Date.now(),
      questionCount: 0,
      events: []
    });

    this.logger.info('Nova simulação criada', 'SIMULATION_CREATED', {
      simulationId,
      userId,
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }

  // Log de início de simulação
  logSimulationStarted(simulationId, initialState) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.info('Simulação iniciada', 'SIMULATION_STARTED', {
      simulationId,
      userId: session?.userId,
      initialAbility: initialState.currentAbility,
      initialStandardError: initialState.standardError,
      targetQuestions: { min: 75, max: 265 },
      algorithm: 'TRI_ADAPTIVE'
    });

    if (session) {
      session.events.push({
        type: 'STARTED',
        timestamp: Date.now(),
        data: initialState
      });
    }
  }

  // Log de apresentação de questão
  logQuestionPresented(simulationId, questionData) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.info('Questão apresentada ao usuário', 'QUESTION_PRESENTED', {
      simulationId,
      userId: session?.userId,
      questionId: questionData.id,
      questionNumber: (session?.questionCount || 0) + 1,
      difficulty: questionData.difficulty,
      contentArea: questionData.content_area,
      questionType: questionData.question_type,
      estimatedTime: questionData.estimated_time || 90
    });

    if (session) {
      session.questionCount++;
      session.lastQuestionTime = Date.now();
      session.events.push({
        type: 'QUESTION_PRESENTED',
        timestamp: Date.now(),
        data: {
          questionId: questionData.id,
          difficulty: questionData.difficulty,
          contentArea: questionData.content_area
        }
      });
    }
  }

  // Log de resposta submetida
  logResponseSubmitted(simulationId, responseData) {
    const session = this.activeSessions.get(simulationId);
    const responseTime = session?.lastQuestionTime ? 
      Date.now() - session.lastQuestionTime : null;

    this.logger.info('Resposta submetida pelo usuário', 'RESPONSE_SUBMITTED', {
      simulationId,
      userId: session?.userId,
      questionId: responseData.questionId,
      questionNumber: session?.questionCount || 0,
      selectedOption: responseData.selectedOption,
      isCorrect: responseData.isCorrect,
      responseTime,
      confidence: responseData.confidence || null,
      timeSpent: responseTime
    });

    if (session) {
      session.events.push({
        type: 'RESPONSE_SUBMITTED',
        timestamp: Date.now(),
        data: {
          questionId: responseData.questionId,
          isCorrect: responseData.isCorrect,
          responseTime
        }
      });
    }
  }

  // Log de atualização de estado TRI
  logTRIStateUpdate(simulationId, previousState, newState) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.debug('Estado TRI atualizado', 'TRI_STATE_UPDATE', {
      simulationId,
      userId: session?.userId,
      questionCount: newState.questionCount,
      abilityChange: {
        previous: previousState.currentAbility,
        new: newState.currentAbility,
        delta: newState.currentAbility - previousState.currentAbility
      },
      standardError: {
        previous: previousState.standardError,
        new: newState.standardError,
        improvement: previousState.standardError - newState.standardError
      },
      confidence: newState.confidence,
      isComplete: newState.isComplete
    });

    if (session) {
      session.events.push({
        type: 'TRI_STATE_UPDATE',
        timestamp: Date.now(),
        data: {
          ability: newState.currentAbility,
          standardError: newState.standardError,
          confidence: newState.confidence
        }
      });
    }
  }

  // Log de pausa na simulação
  logSimulationPaused(simulationId, reason = 'USER_REQUEST') {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.info('Simulação pausada', 'SIMULATION_PAUSED', {
      simulationId,
      userId: session?.userId,
      reason,
      currentQuestion: session?.questionCount || 0,
      duration: session ? Date.now() - session.startTime : 0
    });

    if (session) {
      session.events.push({
        type: 'PAUSED',
        timestamp: Date.now(),
        data: { reason }
      });
    }
  }

  // Log de retomada da simulação
  logSimulationResumed(simulationId) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.info('Simulação retomada', 'SIMULATION_RESUMED', {
      simulationId,
      userId: session?.userId,
      currentQuestion: session?.questionCount || 0,
      totalPauseDuration: this.calculatePauseDuration(session)
    });

    if (session) {
      session.events.push({
        type: 'RESUMED',
        timestamp: Date.now(),
        data: {}
      });
    }
  }

  // Log de conclusão da simulação
  logSimulationCompleted(simulationId, finalResults) {
    const session = this.activeSessions.get(simulationId);
    const totalDuration = session ? Date.now() - session.startTime : 0;
    
    this.logger.info('Simulação concluída', 'SIMULATION_COMPLETED', {
      simulationId,
      userId: session?.userId,
      totalDuration,
      totalQuestions: finalResults.questionCount,
      finalAbility: finalResults.currentAbility,
      finalStandardError: finalResults.standardError,
      result: finalResults.result,
      confidence: finalResults.confidence,
      accuracy: (finalResults.correctAnswers / finalResults.questionCount) * 100,
      averageTimePerQuestion: totalDuration / finalResults.questionCount,
      terminationReason: finalResults.terminationReason
    });

    if (session) {
      session.events.push({
        type: 'COMPLETED',
        timestamp: Date.now(),
        data: finalResults
      });
    }
  }

  // Log de abandono da simulação
  logSimulationAbandoned(simulationId, reason = 'USER_EXIT') {
    const session = this.activeSessions.get(simulationId);
    const duration = session ? Date.now() - session.startTime : 0;
    
    this.logger.warn('Simulação abandonada', 'SIMULATION_ABANDONED', {
      simulationId,
      userId: session?.userId,
      reason,
      questionsCompleted: session?.questionCount || 0,
      duration,
      completionRate: session ? (session.questionCount / 75) * 100 : 0 // Baseado no mínimo de 75 questões
    });

    if (session) {
      session.events.push({
        type: 'ABANDONED',
        timestamp: Date.now(),
        data: { reason }
      });
    }
  }

  // Log de erro durante simulação
  logSimulationError(simulationId, error, context = {}) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.error('Erro durante simulação', 'SIMULATION_ERROR', {
      simulationId,
      userId: session?.userId,
      errorType: error.constructor.name,
      errorMessage: error.message,
      stack: error.stack,
      currentQuestion: session?.questionCount || 0,
      context
    });

    if (session) {
      session.events.push({
        type: 'ERROR',
        timestamp: Date.now(),
        data: {
          error: error.message,
          context
        }
      });
    }
  }

  // Log de métricas de performance
  logPerformanceMetrics(simulationId, metrics) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.debug('Métricas de performance da simulação', 'PERFORMANCE_METRICS', {
      simulationId,
      userId: session?.userId,
      averageResponseTime: metrics.averageResponseTime,
      questionSelectionTime: metrics.questionSelectionTime,
      triCalculationTime: metrics.triCalculationTime,
      databaseQueryTime: metrics.databaseQueryTime,
      llmGenerationTime: metrics.llmGenerationTime,
      totalProcessingTime: metrics.totalProcessingTime,
      efficiency: metrics.efficiency
    });
  }

  // Log de análise de padrões de resposta
  logResponsePatterns(simulationId, patterns) {
    const session = this.activeSessions.get(simulationId);
    
    this.logger.debug('Padrões de resposta identificados', 'RESPONSE_PATTERNS', {
      simulationId,
      userId: session?.userId,
      streakAnalysis: patterns.streakAnalysis,
      difficultyProgression: patterns.difficultyProgression,
      contentAreaPerformance: patterns.contentAreaPerformance,
      timePatterns: patterns.timePatterns,
      confidenceCorrelation: patterns.confidenceCorrelation
    });
  }

  // Obter estatísticas da sessão
  getSessionStats(simulationId) {
    const session = this.activeSessions.get(simulationId);
    if (!session) return null;

    return {
      simulationId,
      userId: session.userId,
      duration: Date.now() - session.startTime,
      questionCount: session.questionCount,
      eventsCount: session.events.length,
      averageTimePerQuestion: session.questionCount > 0 ? 
        (Date.now() - session.startTime) / session.questionCount : 0,
      eventTypes: this.getEventTypeDistribution(session.events)
    };
  }

  // Obter distribuição de tipos de eventos
  getEventTypeDistribution(events) {
    const distribution = {};
    events.forEach(event => {
      distribution[event.type] = (distribution[event.type] || 0) + 1;
    });
    return distribution;
  }

  // Calcular duração total de pausas
  calculatePauseDuration(session) {
    if (!session) return 0;
    
    let totalPause = 0;
    let pauseStart = null;
    
    session.events.forEach(event => {
      if (event.type === 'PAUSED') {
        pauseStart = event.timestamp;
      } else if (event.type === 'RESUMED' && pauseStart) {
        totalPause += event.timestamp - pauseStart;
        pauseStart = null;
      }
    });
    
    return totalPause;
  }

  // Finalizar sessão e limpar da memória
  endSession(simulationId) {
    const session = this.activeSessions.get(simulationId);
    if (session) {
      this.logger.debug('Sessão de simulação finalizada', 'SESSION_ENDED', {
        simulationId,
        userId: session.userId,
        totalDuration: Date.now() - session.startTime,
        totalEvents: session.events.length
      });
      
      this.activeSessions.delete(simulationId);
    }
  }

  // Obter todas as sessões ativas
  getActiveSessions() {
    return Array.from(this.activeSessions.entries()).map(([id, session]) => ({
      simulationId: id,
      userId: session.userId,
      duration: Date.now() - session.startTime,
      questionCount: session.questionCount,
      eventsCount: session.events.length
    }));
  }

  // Log de métricas periódicas
  logPeriodicMetrics() {
    const activeSessions = this.getActiveSessions();
    
    if (activeSessions.length > 0) {
      const totalQuestions = activeSessions.reduce((acc, session) => acc + session.questionCount, 0);
      const avgDuration = activeSessions.reduce((acc, session) => acc + session.duration, 0) / activeSessions.length;
      
      this.logger.info('Métricas periódicas de simulação', 'PERIODIC_METRICS', {
        activeSimulations: activeSessions.length,
        totalQuestionsAnswered: totalQuestions,
        averageSessionDuration: Math.round(avgDuration),
        sessionsDetails: activeSessions
      });
    }
  }
}

// Instância singleton
const simulationLogger = new SimulationLogger();

module.exports = simulationLogger;
