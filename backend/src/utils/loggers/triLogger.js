const { createLogger } = require('../logger');

class TRILogger {
  constructor() {
    this.logger = createLogger('TRI');
    this.simulationSessions = new Map();
  }

  // Log de inicialização de simulação TRI
  logSimulationInit(userId, simulationId, initialState) {
    const sessionId = `tri_${simulationId}_${Date.now()}`;
    
    this.simulationSessions.set(simulationId, {
      sessionId,
      userId,
      startTime: Date.now(),
      questionCount: 0,
      abilityHistory: [initialState.currentAbility],
      responses: []
    });

    this.logger.info('Simulação TRI inicializada', 'SIMULATION_INIT', {
      sessionId,
      userId,
      simulationId,
      initialAbility: initialState.currentAbility,
      initialStandardError: initialState.standardError,
      minQuestions: 75,
      maxQuestions: 265,
      passingThreshold: 0.0
    });
  }

  // Log de seleção de próxima questão
  logQuestionSelection(simulationId, selectedQuestion, selectionCriteria) {
    const session = this.simulationSessions.get(simulationId);
    
    this.logger.debug('Questão selecionada pelo algoritmo TRI', 'QUESTION_SELECTION', {
      sessionId: session?.sessionId,
      simulationId,
      questionId: selectedQuestion.id,
      difficulty: selectedQuestion.difficulty,
      contentArea: selectedQuestion.content_area,
      currentAbility: selectionCriteria.currentAbility,
      targetDifficulty: selectionCriteria.targetDifficulty,
      informationValue: selectionCriteria.informationValue,
      selectionMethod: selectionCriteria.method || 'MAXIMUM_INFORMATION'
    });
  }

  // Log de resposta do usuário
  logUserResponse(simulationId, questionId, userResponse, isCorrect) {
    const session = this.simulationSessions.get(simulationId);
    
    if (session) {
      session.questionCount++;
      session.responses.push({
        questionId,
        userResponse,
        isCorrect,
        timestamp: new Date().toISOString()
      });
    }

    this.logger.info('Resposta do usuário registrada', 'USER_RESPONSE', {
      sessionId: session?.sessionId,
      simulationId,
      questionId,
      questionNumber: session?.questionCount || 0,
      userResponse,
      isCorrect,
      responseTime: Date.now() - (session?.lastQuestionTime || Date.now())
    });
  }

  // Log de cálculo MLE (Maximum Likelihood Estimation)
  logMLECalculation(simulationId, mleData) {
    const session = this.simulationSessions.get(simulationId);
    
    this.logger.debug('Cálculo MLE da habilidade', 'MLE_CALCULATION', {
      sessionId: session?.sessionId,
      simulationId,
      previousAbility: mleData.previousAbility,
      newAbility: mleData.newAbility,
      abilityChange: mleData.newAbility - mleData.previousAbility,
      iterations: mleData.iterations,
      convergence: mleData.convergence,
      standardError: mleData.standardError,
      responses: mleData.responses.length,
      likelihood: mleData.likelihood
    });

    // Atualizar histórico da sessão
    if (session) {
      session.abilityHistory.push(mleData.newAbility);
    }
  }

  // Log de função de resposta ao item
  logItemResponseFunction(simulationId, ability, difficulty, probability) {
    this.logger.debug('Função de Resposta ao Item calculada', 'IRF_CALCULATION', {
      sessionId: this.simulationSessions.get(simulationId)?.sessionId,
      simulationId,
      ability,
      difficulty,
      probability,
      discrimination: 1.0, // Modelo 1PL/Rasch
      model: 'RASCH_1PL'
    });
  }

  // Log de critérios de terminação
  logTerminationCheck(simulationId, terminationData) {
    const session = this.simulationSessions.get(simulationId);
    
    this.logger.debug('Verificação de critérios de terminação', 'TERMINATION_CHECK', {
      sessionId: session?.sessionId,
      simulationId,
      questionCount: terminationData.questionCount,
      currentAbility: terminationData.currentAbility,
      standardError: terminationData.standardError,
      confidence: terminationData.confidence,
      minQuestionsReached: terminationData.questionCount >= 75,
      maxQuestionsReached: terminationData.questionCount >= 265,
      confidenceThresholdMet: terminationData.confidence >= 95,
      shouldTerminate: terminationData.shouldTerminate,
      terminationReason: terminationData.terminationReason
    });
  }

  // Log de decisão de aprovação/reprovação
  logPassFailDecision(simulationId, decisionData) {
    const session = this.simulationSessions.get(simulationId);
    
    this.logger.info('Decisão de aprovação/reprovação', 'PASS_FAIL_DECISION', {
      sessionId: session?.sessionId,
      simulationId,
      finalAbility: decisionData.finalAbility,
      passingThreshold: decisionData.passingThreshold,
      result: decisionData.result,
      confidence: decisionData.confidence,
      standardError: decisionData.standardError,
      totalQuestions: decisionData.totalQuestions,
      correctAnswers: decisionData.correctAnswers,
      accuracy: decisionData.accuracy,
      passProbability: decisionData.passProbability
    });
  }

  // Log de relatório TRI final
  logTRIReport(simulationId, report) {
    const session = this.simulationSessions.get(simulationId);
    const duration = session ? Date.now() - session.startTime : 0;
    
    this.logger.info('Relatório TRI gerado', 'TRI_REPORT', {
      sessionId: session?.sessionId,
      simulationId,
      duration,
      overview: report.overview,
      ability: report.ability,
      progression: {
        abilityEvolution: report.progression.abilityHistory,
        difficultyProgression: report.progression.questionDifficulties,
        accuracyPattern: report.progression.correctnessPattern
      },
      interpretation: report.interpretation
    });
  }

  // Log de análise de convergência
  logConvergenceAnalysis(simulationId, convergenceData) {
    const session = this.simulationSessions.get(simulationId);
    
    this.logger.debug('Análise de convergência do algoritmo', 'CONVERGENCE_ANALYSIS', {
      sessionId: session?.sessionId,
      simulationId,
      abilityStability: convergenceData.abilityStability,
      standardErrorTrend: convergenceData.standardErrorTrend,
      convergenceRate: convergenceData.convergenceRate,
      oscillationCount: convergenceData.oscillationCount,
      isConverged: convergenceData.isConverged,
      confidenceLevel: convergenceData.confidenceLevel
    });
  }

  // Log de métricas de informação
  logInformationMetrics(simulationId, informationData) {
    this.logger.debug('Métricas de informação TRI', 'INFORMATION_METRICS', {
      sessionId: this.simulationSessions.get(simulationId)?.sessionId,
      simulationId,
      totalInformation: informationData.totalInformation,
      averageInformation: informationData.averageInformation,
      informationGain: informationData.informationGain,
      efficiency: informationData.efficiency,
      optimalityIndex: informationData.optimalityIndex
    });
  }

  // Log de erro ou anomalia no algoritmo TRI
  logTRIError(simulationId, error, context = {}) {
    const session = this.simulationSessions.get(simulationId);
    
    this.logger.error('Erro no algoritmo TRI', 'TRI_ERROR', {
      sessionId: session?.sessionId,
      simulationId,
      errorType: error.constructor.name,
      errorMessage: error.message,
      stack: error.stack,
      context,
      currentState: session ? {
        questionCount: session.questionCount,
        lastAbility: session.abilityHistory[session.abilityHistory.length - 1]
      } : null
    });
  }

  // Log de finalização de simulação
  logSimulationEnd(simulationId, finalResults) {
    const session = this.simulationSessions.get(simulationId);
    const duration = session ? Date.now() - session.startTime : 0;
    
    this.logger.info('Simulação TRI finalizada', 'SIMULATION_END', {
      sessionId: session?.sessionId,
      simulationId,
      duration,
      totalQuestions: finalResults.questionCount,
      finalAbility: finalResults.currentAbility,
      result: finalResults.result,
      confidence: finalResults.confidence,
      efficiency: session ? (session.questionCount / duration) * 1000 : 0, // questões por segundo
      abilityProgression: session?.abilityHistory || []
    });

    // Limpar sessão da memória
    this.simulationSessions.delete(simulationId);
  }

  // Obter estatísticas da sessão atual
  getSessionStats(simulationId) {
    const session = this.simulationSessions.get(simulationId);
    if (!session) return null;

    return {
      sessionId: session.sessionId,
      duration: Date.now() - session.startTime,
      questionCount: session.questionCount,
      currentAbility: session.abilityHistory[session.abilityHistory.length - 1],
      abilityStability: this.calculateAbilityStability(session.abilityHistory),
      responsePattern: session.responses.map(r => r.isCorrect)
    };
  }

  // Calcular estabilidade da habilidade
  calculateAbilityStability(abilityHistory) {
    if (abilityHistory.length < 3) return 0;
    
    const recentAbilities = abilityHistory.slice(-5);
    const variance = this.calculateVariance(recentAbilities);
    
    return Math.max(0, 1 - variance); // 1 = muito estável, 0 = muito instável
  }

  // Calcular variância
  calculateVariance(values) {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
  }

  // Log de métricas periódicas
  logPeriodicMetrics() {
    const activeSessions = this.simulationSessions.size;
    const totalSessions = Array.from(this.simulationSessions.values());
    
    if (totalSessions.length > 0) {
      const avgDuration = totalSessions.reduce((acc, session) => 
        acc + (Date.now() - session.startTime), 0) / totalSessions.length;
      
      const avgQuestions = totalSessions.reduce((acc, session) => 
        acc + session.questionCount, 0) / totalSessions.length;

      this.logger.info('Métricas TRI periódicas', 'PERIODIC_METRICS', {
        activeSessions,
        averageSessionDuration: Math.round(avgDuration),
        averageQuestionsPerSession: Math.round(avgQuestions * 100) / 100,
        timestamp: new Date().toISOString()
      });
    }
  }
}

// Instância singleton
const triLogger = new TRILogger();

module.exports = triLogger;
