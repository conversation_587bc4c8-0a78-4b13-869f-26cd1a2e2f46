#!/usr/bin/env node

/**
 * Demonstração do sistema de logging expandido
 * Mostra como o logging captura chamadas LLM e fluxo TRI
 */

const llmLogger = require('./src/utils/loggers/llmLogger');
const triLogger = require('./src/utils/loggers/triLogger');
const simulationLogger = require('./src/utils/loggers/simulationLogger');

console.log('🎯 DEMONSTRAÇÃO DO SISTEMA DE LOGGING EXPANDIDO');
console.log('═'.repeat(60));
console.log();

// Simular uma simulação completa
async function demonstrateLogging() {
  const simulationId = 'demo_sim_001';
  const userId = 'demo_user_123';

  console.log('📋 1. INICIANDO SIMULAÇÃO');
  console.log('─'.repeat(30));
  
  // 1. Criar simulação
  simulationLogger.logSimulationCreated(simulationId, userId, {
    source: 'DEMO',
    timestamp: new Date().toISOString()
  });

  // 2. Inicializar TRI
  const initialState = {
    userId,
    currentAbility: 0.0,
    abilityHistory: [0.0],
    standardError: 1.0,
    questionCount: 0,
    correctAnswers: 0,
    responses: [],
    isComplete: false,
    result: null,
    confidence: 0.0
  };

  triLogger.logSimulationInit(userId, simulationId, initialState);
  simulationLogger.logSimulationStarted(simulationId, initialState);

  console.log('\n🧠 2. CHAMADAS LLM PARA GERAÇÃO DE QUESTÕES');
  console.log('─'.repeat(40));

  // 3. Simular geração de questões via LLM
  for (let i = 1; i <= 3; i++) {
    const callId = `demo_llm_${i}_${Date.now()}`;
    
    const requestData = {
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'Você é um especialista em NCLEX...' },
        { role: 'user', content: `Gere uma questão de dificuldade ${i * 0.3 - 0.5}` }
      ],
      temperature: 0.8,
      max_tokens: 1500
    };

    // Log início da chamada
    llmLogger.logLLMCallStart(callId, requestData);
    
    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));
    
    // Simular resposta da API
    const responseData = {
      usage: {
        total_tokens: 120 + Math.floor(Math.random() * 80),
        prompt_tokens: 40 + Math.floor(Math.random() * 20),
        completion_tokens: 80 + Math.floor(Math.random() * 60)
      },
      choices: [{
        message: { 
          content: JSON.stringify({
            question_text: `Questão NCLEX ${i}`,
            difficulty: i * 0.3 - 0.5,
            content_area: 'Safe and Effective Care Environment'
          })
        },
        finish_reason: 'stop'
      }]
    };

    const duration = 200 + Math.random() * 300;
    
    // Log sucesso da chamada
    llmLogger.logLLMCallSuccess(callId, requestData, responseData, duration);
    
    // Log geração da questão
    llmLogger.logQuestionGeneration({
      difficulty: i * 0.3 - 0.5,
      content_area: 'Safe and Effective Care Environment',
      question_type: 'multiple_choice'
    }, {
      questionNumber: i,
      source: 'DEMO'
    });

    console.log(`   ✓ Questão ${i} gerada - ${Math.round(duration)}ms - ${responseData.usage.total_tokens} tokens`);
  }

  console.log('\n📊 3. FLUXO DO ALGORITMO TRI');
  console.log('─'.repeat(30));

  // 4. Simular respostas e cálculos TRI
  let currentAbility = 0.0;
  let standardError = 1.0;

  for (let q = 1; q <= 5; q++) {
    const questionId = `demo_q_${q}`;
    const difficulty = (q - 3) * 0.4; // Varia de -0.8 a 0.8
    const isCorrect = Math.random() > 0.4; // 60% de chance de acerto

    // Log apresentação da questão
    simulationLogger.logQuestionPresented(simulationId, {
      id: questionId,
      difficulty,
      content_area: 'Safe and Effective Care Environment',
      question_type: 'multiple_choice'
    });

    // Log resposta do usuário
    triLogger.logUserResponse(simulationId, questionId, q, isCorrect);
    simulationLogger.logResponseSubmitted(simulationId, {
      questionId,
      selectedOption: ['A'],
      isCorrect
    });

    // Simular cálculo MLE
    const previousAbility = currentAbility;
    currentAbility += isCorrect ? 0.15 : -0.1;
    currentAbility += (Math.random() - 0.5) * 0.1; // Adicionar variação
    standardError = Math.max(0.2, standardError - 0.15);

    // Log cálculo MLE
    triLogger.logMLECalculation(simulationId, {
      previousAbility,
      newAbility: currentAbility,
      iterations: 3 + Math.floor(Math.random() * 5),
      convergence: true,
      standardError,
      responses: Array(q).fill({}),
      likelihood: Math.random() * 0.5 + 0.5
    });

    // Log verificação de terminação
    const confidence = Math.min(95, q * 18);
    const shouldTerminate = q >= 5 || confidence >= 95;
    
    triLogger.logTerminationCheck(simulationId, {
      questionCount: q,
      currentAbility,
      standardError,
      confidence,
      shouldTerminate,
      terminationReason: shouldTerminate ? 
        (q >= 5 ? 'DEMO_LIMIT_REACHED' : 'CONFIDENCE_THRESHOLD_MET') : null
    });

    console.log(`   Q${q}: ${isCorrect ? '✓' : '✗'} | Habilidade: ${currentAbility.toFixed(3)} | SE: ${standardError.toFixed(3)} | Conf: ${confidence}%`);

    // Se deve terminar
    if (shouldTerminate) {
      const result = currentAbility > 0 ? 'pass' : 'fail';
      
      // Log decisão final
      triLogger.logPassFailDecision(simulationId, {
        finalAbility: currentAbility,
        passingThreshold: 0.0,
        result,
        confidence,
        standardError,
        totalQuestions: q,
        correctAnswers: Math.floor(q * 0.6),
        accuracy: 60,
        passProbability: result === 'pass' ? confidence : (100 - confidence)
      });

      // Log relatório TRI
      const report = {
        overview: {
          totalQuestions: q,
          correctAnswers: Math.floor(q * 0.6),
          accuracy: 60,
          result,
          confidence
        },
        ability: {
          estimated: currentAbility,
          percentile: 50 + currentAbility * 20,
          standardError,
          passProbability: result === 'pass' ? confidence : (100 - confidence)
        },
        progression: {
          abilityHistory: Array(q + 1).fill(0).map((_, i) => i * 0.1),
          questionDifficulties: Array(q).fill(0).map((_, i) => (i - 2) * 0.4),
          correctnessPattern: Array(q).fill(0).map(() => Math.random() > 0.4)
        }
      };

      triLogger.logTRIReport(simulationId, report);
      simulationLogger.logSimulationCompleted(simulationId, {
        questionCount: q,
        currentAbility,
        standardError,
        result,
        confidence,
        correctAnswers: Math.floor(q * 0.6),
        terminationReason: shouldTerminate ? 'DEMO_COMPLETED' : null
      });

      break;
    }
  }

  console.log('\n📈 4. MÉTRICAS FINAIS');
  console.log('─'.repeat(20));

  // 5. Exibir métricas
  const llmMetrics = llmLogger.getMetrics();
  console.log('\n🧠 Métricas LLM:');
  console.log(`   • Total de chamadas: ${llmMetrics.totalCalls}`);
  console.log(`   • Tokens utilizados: ${llmMetrics.totalTokens}`);
  console.log(`   • Custo estimado: $${llmMetrics.totalCost.toFixed(4)}`);
  console.log(`   • Latência média: ${Math.round(llmMetrics.averageLatency)}ms`);
  console.log(`   • Taxa de sucesso: ${llmMetrics.successRate.toFixed(1)}%`);

  const sessionStats = simulationLogger.getSessionStats(simulationId);
  if (sessionStats) {
    console.log('\n🎯 Estatísticas da Simulação:');
    console.log(`   • Duração total: ${Math.round(sessionStats.duration / 1000)}s`);
    console.log(`   • Questões respondidas: ${sessionStats.questionCount}`);
    console.log(`   • Eventos registrados: ${sessionStats.eventsCount}`);
    console.log(`   • Tempo médio por questão: ${Math.round(sessionStats.averageTimePerQuestion / 1000)}s`);
  }

  // Finalizar sessão
  triLogger.logSimulationEnd(simulationId, {
    questionCount: 5,
    currentAbility,
    result: currentAbility > 0 ? 'pass' : 'fail',
    confidence: 90,
    isComplete: true
  });
  
  simulationLogger.endSession(simulationId);

  console.log('\n✅ DEMONSTRAÇÃO CONCLUÍDA!');
  console.log('\n📁 Logs salvos em:');
  console.log('   • logs/application-YYYY-MM-DD.log (logs estruturados)');
  console.log('   • logs/error-YYYY-MM-DD.log (apenas erros)');
  console.log('   • logs/debug-YYYY-MM-DD.log (debug detalhado)');
  
  console.log('\n🔧 Para monitorar em tempo real:');
  console.log('   • node monitor-logs.js start');
  console.log('   • node monitor-logs.js metrics');
  console.log('   • curl http://localhost:5000/api/metrics');
}

// Executar demonstração
if (require.main === module) {
  demonstrateLogging().catch(console.error);
}

module.exports = { demonstrateLogging };
