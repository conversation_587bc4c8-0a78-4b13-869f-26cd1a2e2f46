{"action":"SIMULATION_CREATED","level":"info","message":"Nova simulação criada","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.297","userId":"user_test_456"}
{"action":"SIMULATION_INIT","initialAbility":0,"initialStandardError":1,"level":"info","maxQuestions":265,"message":"Simulação TRI inicializada","minQuestions":75,"passingThreshold":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.298","userId":"user_test_456"}
{"action":"SIMULATION_STARTED","algorithm":"TRI_ADAPTIVE","initialAbility":0,"initialStandardError":1,"level":"info","message":"Simulação iniciada","service":"SIMULATION","simulationId":"sim_test_123","targetQuestions":{"max":265,"min":75},"timestamp":"2025-08-13 16:44:46.299","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.44382011375156294,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_1","questionNumber":1,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.301","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_1_1755114286301","completionTokens":100,"duration":208,"estimatedCost":0.00501,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:44:46.509","tokensUsed":167}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_1","questionNumber":1,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.511","userResponse":1}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_1","questionNumber":1,"responseTime":210,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":210,"timestamp":"2025-08-13 16:44:46.512","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.2555769141230373,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_2","questionNumber":2,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.513","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_2_1755114286513","completionTokens":100,"duration":236,"estimatedCost":0.00498,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:44:46.749","tokensUsed":166}
{"action":"USER_RESPONSE","isCorrect":false,"level":"info","message":"Resposta do usuário registrada","questionId":"q_2","questionNumber":2,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.750","userResponse":2}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":false,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_2","questionNumber":2,"responseTime":237,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":237,"timestamp":"2025-08-13 16:44:46.750","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.8010654436593092,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_3","questionNumber":3,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.751","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_3_1755114286751","completionTokens":100,"duration":102,"estimatedCost":0.00573,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:44:46.853","tokensUsed":191}
{"action":"USER_RESPONSE","isCorrect":false,"level":"info","message":"Resposta do usuário registrada","questionId":"q_3","questionNumber":3,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.854","userResponse":3}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":false,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_3","questionNumber":3,"responseTime":103,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":103,"timestamp":"2025-08-13 16:44:46.854","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.20736087007280224,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_4","questionNumber":4,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:46.854","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_4_1755114286855","completionTokens":100,"duration":248,"estimatedCost":0.0048,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:44:47.103","tokensUsed":160}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_4","questionNumber":4,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:47.103","userResponse":4}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_4","questionNumber":4,"responseTime":248,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":248,"timestamp":"2025-08-13 16:44:47.103","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.7418548865387158,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_5","questionNumber":5,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:47.104","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_5_1755114287104","completionTokens":100,"duration":177,"estimatedCost":0.00591,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:44:47.281","tokensUsed":197}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_5","questionNumber":5,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:47.281","userResponse":5}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_5","questionNumber":5,"responseTime":177,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":177,"timestamp":"2025-08-13 16:44:47.281","userId":"user_test_456"}
{"accuracy":60,"action":"PASS_FAIL_DECISION","confidence":75,"correctAnswers":3,"finalAbility":0.009859267251870293,"level":"info","message":"Decisão de aprovação/reprovação","passProbability":75,"passingThreshold":0,"result":"pass","service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","standardError":0.5,"timestamp":"2025-08-13 16:44:47.282","totalQuestions":5}
{"ability":{"estimated":0.009859267251870293,"passProbability":75,"percentile":50.197185345037404,"standardError":0.5},"action":"TRI_REPORT","duration":984,"level":"info","message":"Relatório TRI gerado","overview":{"accuracy":60,"confidence":75,"correctAnswers":3,"result":"pass","totalQuestions":5},"progression":{"abilityEvolution":[0,0.1,0.2,0.30000000000000004,0.4,0.5],"accuracyPattern":[true,true,true,false,true],"difficultyProgression":[0.9577631492799759,-0.06490185006951421,0.8022950697611018,0.6951733324527987,0.9026655682846294]},"service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:47.282"}
{"accuracy":60,"action":"SIMULATION_COMPLETED","averageTimePerQuestion":197.6,"confidence":75,"finalAbility":0.009859267251870293,"finalStandardError":0.5,"level":"info","message":"Simulação concluída","result":"pass","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:47.283","totalDuration":988,"totalQuestions":5,"userId":"user_test_456"}
{"abilityProgression":[0,0.11533472424433465,-0.06374596319012196,-0.07481939795868474,0.0031482681203498473,0.009859267251870293],"action":"SIMULATION_END","confidence":75,"duration":985,"efficiency":5.076142131979695,"finalAbility":0.009859267251870293,"level":"info","message":"Simulação TRI finalizada","result":"pass","service":"TRI","sessionId":"tri_sim_test_123_1755114286298","simulationId":"sim_test_123","timestamp":"2025-08-13 16:44:47.283","totalQuestions":5}
{"action":"SIMULATION_CREATED","level":"info","message":"Nova simulação criada","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.273","userId":"user_test_456"}
{"action":"SIMULATION_INIT","initialAbility":0,"initialStandardError":1,"level":"info","maxQuestions":265,"message":"Simulação TRI inicializada","minQuestions":75,"passingThreshold":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.274","userId":"user_test_456"}
{"action":"SIMULATION_STARTED","algorithm":"TRI_ADAPTIVE","initialAbility":0,"initialStandardError":1,"level":"info","message":"Simulação iniciada","service":"SIMULATION","simulationId":"sim_test_123","targetQuestions":{"max":265,"min":75},"timestamp":"2025-08-13 16:45:15.274","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.6332055899669893,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_1","questionNumber":1,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.276","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_1_1755114315276","completionTokens":100,"duration":153,"estimatedCost":0.00579,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:45:15.429","tokensUsed":193}
{"action":"USER_RESPONSE","isCorrect":false,"level":"info","message":"Resposta do usuário registrada","questionId":"q_1","questionNumber":1,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.430","userResponse":1}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":false,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_1","questionNumber":1,"responseTime":155,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":155,"timestamp":"2025-08-13 16:45:15.431","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.0006531994877558844,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_2","questionNumber":2,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.433","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_2_1755114315434","completionTokens":100,"duration":104,"estimatedCost":0.005339999999999999,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:45:15.539","tokensUsed":178}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_2","questionNumber":2,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.539","userResponse":2}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_2","questionNumber":2,"responseTime":105,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":105,"timestamp":"2025-08-13 16:45:15.539","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.23758808614235027,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_3","questionNumber":3,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.540","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_3_1755114315540","completionTokens":100,"duration":122,"estimatedCost":0.0056099999999999995,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:45:15.662","tokensUsed":187}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_3","questionNumber":3,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.663","userResponse":3}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_3","questionNumber":3,"responseTime":123,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":123,"timestamp":"2025-08-13 16:45:15.663","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.42277017695962726,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_4","questionNumber":4,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.664","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_4_1755114315664","completionTokens":100,"duration":195,"estimatedCost":0.00465,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:45:15.859","tokensUsed":155}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_4","questionNumber":4,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.859","userResponse":4}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_4","questionNumber":4,"responseTime":195,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":195,"timestamp":"2025-08-13 16:45:15.859","userId":"user_test_456"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.8982884993995102,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"q_5","questionNumber":5,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.860","userId":"user_test_456"}
{"action":"LLM_CALL","actionId":"llm_test_5_1755114315860","completionTokens":100,"duration":105,"estimatedCost":0.00579,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":50,"requestType":"UNKNOWN","responseLength":20,"service":"LLM","timestamp":"2025-08-13 16:45:15.965","tokensUsed":193}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"q_5","questionNumber":5,"responseTime":0,"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.966","userResponse":5}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"q_5","questionNumber":5,"responseTime":106,"selectedOption":["A"],"service":"SIMULATION","simulationId":"sim_test_123","timeSpent":106,"timestamp":"2025-08-13 16:45:15.966","userId":"user_test_456"}
{"accuracy":80,"action":"PASS_FAIL_DECISION","confidence":75,"correctAnswers":4,"finalAbility":0.3082977115010523,"level":"info","message":"Decisão de aprovação/reprovação","passProbability":75,"passingThreshold":0,"result":"pass","service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","standardError":0.5,"timestamp":"2025-08-13 16:45:15.967","totalQuestions":5}
{"ability":{"estimated":0.3082977115010523,"passProbability":75,"percentile":56.16595423002104,"standardError":0.5},"action":"TRI_REPORT","duration":694,"level":"info","message":"Relatório TRI gerado","overview":{"accuracy":80,"confidence":75,"correctAnswers":4,"result":"pass","totalQuestions":5},"progression":{"abilityEvolution":[0,0.1,0.2,0.30000000000000004,0.4,0.5],"accuracyPattern":[true,false,true,true,false],"difficultyProgression":[0.36721130245919786,0.46250117254935663,0.03884448184970335,0.031599219657559185,-0.25433674500415737]},"service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.967"}
{"accuracy":80,"action":"SIMULATION_COMPLETED","averageTimePerQuestion":139.4,"confidence":75,"finalAbility":0.3082977115010523,"finalStandardError":0.5,"level":"info","message":"Simulação concluída","result":"pass","service":"SIMULATION","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.968","totalDuration":697,"totalQuestions":5,"userId":"user_test_456"}
{"abilityProgression":[0,-0.08065177036952909,-0.0736940767336249,0.04465061406469149,0.2101028634552339,0.3082977115010523],"action":"SIMULATION_END","confidence":75,"duration":695,"efficiency":7.194244604316547,"finalAbility":0.3082977115010523,"level":"info","message":"Simulação TRI finalizada","result":"pass","service":"TRI","sessionId":"tri_sim_test_123_1755114315273","simulationId":"sim_test_123","timestamp":"2025-08-13 16:45:15.968","totalQuestions":5}
{"action":"LLM_CALL_ERROR","callId":"llm_error_1755114315969","duration":1000,"errorCode":429,"errorMessage":"Rate limit exceeded","errorType":"Error","level":"error","message":"API rate limit exceeded","model":"gpt-4","requestType":"UNKNOWN","service":"LLM","stack":"Error: API rate limit exceeded\n    at testLogging (/Users/<USER>/frontend-gpt-oss/nclex-simulator/backend/test-logging.js:217:17)","timestamp":"2025-08-13 16:45:15.969"}
{"action":"SIMULATION_CREATED","level":"info","message":"Nova simulação criada","service":"SIMULATION","simulationId":"demo_sim_001","source":"DEMO","timestamp":"2025-08-13 17:07:11.079","userId":"demo_user_123"}
{"action":"SIMULATION_INIT","initialAbility":0,"initialStandardError":1,"level":"info","maxQuestions":265,"message":"Simulação TRI inicializada","minQuestions":75,"passingThreshold":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:11.080","userId":"demo_user_123"}
{"action":"SIMULATION_STARTED","algorithm":"TRI_ADAPTIVE","initialAbility":0,"initialStandardError":1,"level":"info","message":"Simulação iniciada","service":"SIMULATION","simulationId":"demo_sim_001","targetQuestions":{"max":265,"min":75},"timestamp":"2025-08-13 17:07:11.081","userId":"demo_user_123"}
{"action":"LLM_CALL","actionId":"demo_llm_1_1755115631082","completionTokens":86,"duration":393,"estimatedCost":0.00582,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":59,"requestType":"QUESTION_GENERATION","responseLength":106,"service":"LLM","timestamp":"2025-08-13 17:07:11.475","tokensUsed":194}
{"action":"QUESTION_GENERATED","contentArea":"Safe and Effective Care Environment","difficulty":-0.2,"hasRationale":false,"level":"info","message":"Questão gerada pelo LLM","optionsCount":0,"questionNumber":1,"questionType":"multiple_choice","service":"LLM","source":"DEMO","timestamp":"2025-08-13 17:07:11.476"}
{"action":"LLM_CALL","actionId":"demo_llm_2_1755115631476","completionTokens":111,"duration":379,"estimatedCost":0.0048,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":54,"requestType":"QUESTION_GENERATION","responseLength":121,"service":"LLM","timestamp":"2025-08-13 17:07:11.855","tokensUsed":160}
{"action":"QUESTION_GENERATED","contentArea":"Safe and Effective Care Environment","difficulty":0.09999999999999998,"hasRationale":false,"level":"info","message":"Questão gerada pelo LLM","optionsCount":0,"questionNumber":2,"questionType":"multiple_choice","service":"LLM","source":"DEMO","timestamp":"2025-08-13 17:07:11.856"}
{"action":"LLM_CALL","actionId":"demo_llm_3_1755115631856","completionTokens":137,"duration":202,"estimatedCost":0.0045899999999999995,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":47,"requestType":"QUESTION_GENERATION","responseLength":120,"service":"LLM","timestamp":"2025-08-13 17:07:12.058","tokensUsed":153}
{"action":"QUESTION_GENERATED","contentArea":"Safe and Effective Care Environment","difficulty":0.3999999999999999,"hasRationale":false,"level":"info","message":"Questão gerada pelo LLM","optionsCount":0,"questionNumber":3,"questionType":"multiple_choice","service":"LLM","source":"DEMO","timestamp":"2025-08-13 17:07:12.059"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.8,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_1","questionNumber":1,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.060","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":false,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_1","questionNumber":1,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.060","userResponse":1}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":false,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_1","questionNumber":1,"responseTime":1,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":1,"timestamp":"2025-08-13 17:07:12.061","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.4,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_2","questionNumber":2,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.062","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_2","questionNumber":2,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.062","userResponse":2}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_2","questionNumber":2,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 17:07:12.062","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_3","questionNumber":3,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.063","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_3","questionNumber":3,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.063","userResponse":3}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_3","questionNumber":3,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 17:07:12.063","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.4,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_4","questionNumber":4,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.063","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_4","questionNumber":4,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.064","userResponse":4}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_4","questionNumber":4,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 17:07:12.064","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.8,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_5","questionNumber":5,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.064","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_5","questionNumber":5,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.064","userResponse":5}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_5","questionNumber":5,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 17:07:12.065","userId":"demo_user_123"}
{"accuracy":60,"action":"PASS_FAIL_DECISION","confidence":90,"correctAnswers":3,"finalAbility":0.4438514987580893,"level":"info","message":"Decisão de aprovação/reprovação","passProbability":90,"passingThreshold":0,"result":"pass","service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","standardError":0.24999999999999992,"timestamp":"2025-08-13 17:07:12.065","totalQuestions":5}
{"ability":{"estimated":0.4438514987580893,"passProbability":90,"percentile":58.87702997516179,"standardError":0.24999999999999992},"action":"TRI_REPORT","duration":986,"level":"info","message":"Relatório TRI gerado","overview":{"accuracy":60,"confidence":90,"correctAnswers":3,"result":"pass","totalQuestions":5},"progression":{"abilityEvolution":[0,0.1,0.2,0.30000000000000004,0.4,0.5],"accuracyPattern":[true,true,true,false,false],"difficultyProgression":[-0.8,-0.4,0,0.4,0.8]},"service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.066"}
{"accuracy":60,"action":"SIMULATION_COMPLETED","averageTimePerQuestion":197.8,"confidence":90,"finalAbility":0.4438514987580893,"finalStandardError":0.24999999999999992,"level":"info","message":"Simulação concluída","result":"pass","service":"SIMULATION","simulationId":"demo_sim_001","terminationReason":"DEMO_COMPLETED","timestamp":"2025-08-13 17:07:12.066","totalDuration":989,"totalQuestions":5,"userId":"demo_user_123"}
{"abilityProgression":[0,-0.10470459965190665,0.0764834965015177,0.20144706529319345,0.3340711836361735,0.4438514987580893],"action":"SIMULATION_END","confidence":90,"duration":987,"efficiency":5.065856129685917,"finalAbility":0.4438514987580893,"level":"info","message":"Simulação TRI finalizada","result":"pass","service":"TRI","sessionId":"tri_demo_sim_001_1755115631080","simulationId":"demo_sim_001","timestamp":"2025-08-13 17:07:12.067","totalQuestions":5}
{"action":"SIMULATION_CREATED","level":"info","message":"Nova simulação criada","service":"SIMULATION","simulationId":"demo_sim_001","source":"DEMO","timestamp":"2025-08-13 18:34:58.619","userId":"demo_user_123"}
{"action":"SIMULATION_INIT","initialAbility":0,"initialStandardError":1,"level":"info","maxQuestions":265,"message":"Simulação TRI inicializada","minQuestions":75,"passingThreshold":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:58.621","userId":"demo_user_123"}
{"action":"SIMULATION_STARTED","algorithm":"TRI_ADAPTIVE","initialAbility":0,"initialStandardError":1,"level":"info","message":"Simulação iniciada","service":"SIMULATION","simulationId":"demo_sim_001","targetQuestions":{"max":265,"min":75},"timestamp":"2025-08-13 18:34:58.622","userId":"demo_user_123"}
{"action":"LLM_CALL","actionId":"demo_llm_1_1755120898622","completionTokens":81,"duration":373,"estimatedCost":0.00585,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":58,"requestType":"QUESTION_GENERATION","responseLength":106,"service":"LLM","timestamp":"2025-08-13 18:34:58.995","tokensUsed":195}
{"action":"QUESTION_GENERATED","contentArea":"Safe and Effective Care Environment","difficulty":-0.2,"hasRationale":false,"level":"info","message":"Questão gerada pelo LLM","optionsCount":0,"questionNumber":1,"questionType":"multiple_choice","service":"LLM","source":"DEMO","timestamp":"2025-08-13 18:34:58.996"}
{"action":"LLM_CALL","actionId":"demo_llm_2_1755120898996","completionTokens":132,"duration":425,"estimatedCost":0.00531,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":59,"requestType":"QUESTION_GENERATION","responseLength":121,"service":"LLM","timestamp":"2025-08-13 18:34:59.421","tokensUsed":177}
{"action":"QUESTION_GENERATED","contentArea":"Safe and Effective Care Environment","difficulty":0.09999999999999998,"hasRationale":false,"level":"info","message":"Questão gerada pelo LLM","optionsCount":0,"questionNumber":2,"questionType":"multiple_choice","service":"LLM","source":"DEMO","timestamp":"2025-08-13 18:34:59.422"}
{"action":"LLM_CALL","actionId":"demo_llm_3_1755120899422","completionTokens":112,"duration":202,"estimatedCost":0.005339999999999999,"finishReason":"stop","level":"info","message":"Chamada LLM - CONCLUÍDO","model":"gpt-4","promptTokens":54,"requestType":"QUESTION_GENERATION","responseLength":120,"service":"LLM","timestamp":"2025-08-13 18:34:59.624","tokensUsed":178}
{"action":"QUESTION_GENERATED","contentArea":"Safe and Effective Care Environment","difficulty":0.3999999999999999,"hasRationale":false,"level":"info","message":"Questão gerada pelo LLM","optionsCount":0,"questionNumber":3,"questionType":"multiple_choice","service":"LLM","source":"DEMO","timestamp":"2025-08-13 18:34:59.625"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.8,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_1","questionNumber":1,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.626","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_1","questionNumber":1,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.626","userResponse":1}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_1","questionNumber":1,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 18:34:59.626","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":-0.4,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_2","questionNumber":2,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.627","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":false,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_2","questionNumber":2,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.627","userResponse":2}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":false,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_2","questionNumber":2,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 18:34:59.627","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_3","questionNumber":3,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.628","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_3","questionNumber":3,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.628","userResponse":3}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_3","questionNumber":3,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 18:34:59.628","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.4,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_4","questionNumber":4,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.628","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":true,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_4","questionNumber":4,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.628","userResponse":4}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":true,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_4","questionNumber":4,"responseTime":1,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":1,"timestamp":"2025-08-13 18:34:59.629","userId":"demo_user_123"}
{"action":"QUESTION_PRESENTED","contentArea":"Safe and Effective Care Environment","difficulty":0.8,"estimatedTime":90,"level":"info","message":"Questão apresentada ao usuário","questionId":"demo_q_5","questionNumber":5,"questionType":"multiple_choice","service":"SIMULATION","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.629","userId":"demo_user_123"}
{"action":"USER_RESPONSE","isCorrect":false,"level":"info","message":"Resposta do usuário registrada","questionId":"demo_q_5","questionNumber":5,"responseTime":0,"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.630","userResponse":5}
{"action":"RESPONSE_SUBMITTED","confidence":null,"isCorrect":false,"level":"info","message":"Resposta submetida pelo usuário","questionId":"demo_q_5","questionNumber":5,"responseTime":0,"selectedOption":["A"],"service":"SIMULATION","simulationId":"demo_sim_001","timeSpent":0,"timestamp":"2025-08-13 18:34:59.630","userId":"demo_user_123"}
{"accuracy":60,"action":"PASS_FAIL_DECISION","confidence":90,"correctAnswers":3,"finalAbility":0.3003904184316692,"level":"info","message":"Decisão de aprovação/reprovação","passProbability":90,"passingThreshold":0,"result":"pass","service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","standardError":0.24999999999999992,"timestamp":"2025-08-13 18:34:59.631","totalQuestions":5}
{"ability":{"estimated":0.3003904184316692,"passProbability":90,"percentile":56.007808368633384,"standardError":0.24999999999999992},"action":"TRI_REPORT","duration":1011,"level":"info","message":"Relatório TRI gerado","overview":{"accuracy":60,"confidence":90,"correctAnswers":3,"result":"pass","totalQuestions":5},"progression":{"abilityEvolution":[0,0.1,0.2,0.30000000000000004,0.4,0.5],"accuracyPattern":[true,false,false,true,true],"difficultyProgression":[-0.8,-0.4,0,0.4,0.8]},"service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.631"}
{"accuracy":60,"action":"SIMULATION_COMPLETED","averageTimePerQuestion":203.2,"confidence":90,"finalAbility":0.3003904184316692,"finalStandardError":0.24999999999999992,"level":"info","message":"Simulação concluída","result":"pass","service":"SIMULATION","simulationId":"demo_sim_001","terminationReason":"DEMO_COMPLETED","timestamp":"2025-08-13 18:34:59.632","totalDuration":1016,"totalQuestions":5,"userId":"demo_user_123"}
{"abilityProgression":[0,0.19392163731852946,0.07895065075390209,0.2293608537558395,0.3795310875408072,0.3003904184316692],"action":"SIMULATION_END","confidence":90,"duration":1015,"efficiency":4.926108374384237,"finalAbility":0.3003904184316692,"level":"info","message":"Simulação TRI finalizada","result":"pass","service":"TRI","sessionId":"tri_demo_sim_001_1755120898620","simulationId":"demo_sim_001","timestamp":"2025-08-13 18:34:59.635","totalQuestions":5}
