{"action": "LLM_CALL_ERROR", "callId": "llm_error_1755114315969", "duration": 1000, "errorCode": 429, "errorMessage": "Rate limit exceeded", "errorType": "Error", "level": "error", "message": "API rate limit exceeded", "model": "gpt-4", "requestType": "UNKNOWN", "service": "LLM", "stack": "Error: API rate limit exceeded\n    at testLogging (/Users/<USER>/frontend-gpt-oss/nclex-simulator/backend/test-logging.js:217:17)", "timestamp": "2025-08-13 16:45:15.969"}