# 📊 Sistema de Logging Expandido - NCLEX Simulator

## Visão Geral

O sistema de logging expandido captura **todas as ações importantes** do NCLEX Simulator, incluindo:

- 🧠 **Chamadas de API para LLM** (requests, responses, tokens, custos)
- 📊 **Fluxo completo do algoritmo TRI** (cálculos MLE, convergência, decisões)
- 🎯 **Simulações de usuários** (início, progresso, conclusão)
- 🔗 **Requests HTTP** (endpoints, performance, erros)
- ⚡ **Métricas de performance** em tempo real

## Estrutura do Sistema

```
backend/src/utils/
├── logger.js                 # Logger central (Winston)
└── loggers/
    ├── llmLogger.js          # Logs de chamadas LLM
    ├── triLogger.js          # Logs do algoritmo TRI
    ├── simulationLogger.js   # Logs de simulações
    └── terminalLogger.js     # Output colorido no terminal
```

## Arquivos de Log

Os logs são salvos automaticamente em:

```
backend/logs/
├── application-YYYY-MM-DD.log  # Logs gerais (JSON estruturado)
├── error-YYYY-MM-DD.log        # Apenas erros
└── debug-YYYY-MM-DD.log        # Logs detalhados (desenvolvimento)
```

## Funcionalidades Principais

### 🧠 LLM Logger

Captura **todas as interações** com modelos de linguagem:

```javascript
// Exemplo de log LLM
{
  "timestamp": "2025-08-13 17:07:11.475",
  "level": "info",
  "service": "LLM",
  "action": "LLM_CALL",
  "model": "gpt-4",
  "tokensUsed": 194,
  "estimatedCost": 0.00582,
  "duration": 393,
  "requestType": "QUESTION_GENERATION"
}
```

**Métricas capturadas:**
- Total de chamadas e tokens
- Custo estimado por modelo
- Latência média e taxa de sucesso
- Distribuição por tipo de request
- Histórico de chamadas recentes

### 📊 TRI Logger

Monitora **cada etapa** do algoritmo TRI:

```javascript
// Exemplo de log TRI
{
  "timestamp": "2025-08-13 17:07:12.062",
  "level": "debug",
  "service": "TRI",
  "action": "MLE_CALCULATION",
  "previousAbility": 0.0,
  "newAbility": 0.15,
  "iterations": 8,
  "convergence": true,
  "standardError": 0.85
}
```

**Fluxo capturado:**
- Inicialização da simulação
- Seleção de questões
- Respostas dos usuários
- Cálculos MLE (Maximum Likelihood Estimation)
- Verificação de critérios de terminação
- Decisões de aprovação/reprovação
- Relatórios finais

### 🎯 Simulation Logger

Acompanha o **ciclo completo** das simulações:

```javascript
// Exemplo de log de simulação
{
  "timestamp": "2025-08-13 17:07:12.061",
  "level": "info",
  "service": "SIMULATION",
  "action": "RESPONSE_SUBMITTED",
  "simulationId": "demo_sim_001",
  "questionNumber": 1,
  "isCorrect": true,
  "responseTime": 1
}
```

**Estados monitorados:**
- Criação e início
- Apresentação de questões
- Submissão de respostas
- Pausas e retomadas
- Conclusão ou abandono
- Métricas de performance

## Scripts de Monitoramento

### 🔧 Scripts Disponíveis

```bash
# Demonstração do sistema
node demo-logging.js

# Monitor em tempo real
node monitor-logs.js start

# Métricas atuais
node monitor-logs.js metrics

# Análise de logs
node analyze-logs.js analyze

# Relatório de custos LLM
node analyze-logs.js cost

# Performance TRI
node analyze-logs.js tri

# Listar logs disponíveis
node analyze-logs.js list
```

### 📈 Endpoint de Métricas

```bash
# Métricas via API
curl http://localhost:5000/api/metrics
```

Retorna:
```json
{
  "timestamp": "2025-08-13T20:07:12.067Z",
  "llm": {
    "totalCalls": 13,
    "totalTokens": 2294,
    "totalCost": 0.0688,
    "averageLatency": 202,
    "successRate": 100.0
  },
  "simulations": {
    "active": [],
    "count": 0
  },
  "uptime": 1346,
  "memory": {...}
}
```

## Integração no Código

### LLM Service

```javascript
// Antes da chamada
llmLogger.logLLMCallStart(callId, requestData);

// Após sucesso
llmLogger.logLLMCallSuccess(callId, requestData, responseData, duration);

// Em caso de erro
llmLogger.logLLMCallError(callId, requestData, error, duration);
```

### TRI Service

```javascript
// Inicialização
triLogger.logSimulationInit(userId, simulationId, initialState);

// Cálculo MLE
triLogger.logMLECalculation(simulationId, mleData);

// Decisão final
triLogger.logPassFailDecision(simulationId, decisionData);
```

### Controllers

```javascript
// Início de simulação
simulationLogger.logSimulationCreated(simulationId, userId);

// Resposta do usuário
simulationLogger.logResponseSubmitted(simulationId, responseData);

// Conclusão
simulationLogger.logSimulationCompleted(simulationId, finalResults);
```

## Análises Disponíveis

### 💰 Análise de Custos

- Custo total e por chamada
- Tokens utilizados
- Eficiência (tokens/segundo)
- Projeções mensais/anuais
- Distribuição por modelo

### 📊 Análise TRI

- Taxa de convergência do algoritmo
- Iterações médias para convergência
- Distribuição de habilidades
- Taxa de aprovação
- Performance do algoritmo

### 🎯 Análise de Simulações

- Taxa de conclusão
- Duração média
- Questões por simulação
- Padrões de abandono
- Métricas de engajamento

### ⚡ Análise de Performance

- Operações mais lentas
- Frequência de operações
- Horários de pico
- Gargalos identificados

## Configuração

### Variáveis de Ambiente

```bash
# Nível de logging
LOG_LEVEL=info          # debug, info, warn, error

# Ambiente
NODE_ENV=development    # Habilita logs de debug
```

### Personalização

```javascript
// Habilitar/desabilitar terminal logging
terminalLogger.setEnabled(true);

// Ajustar taxa de refresh do monitor
monitor.refreshRate = 3000; // 3 segundos

// Configurar rotação de logs
// Automática: 14 dias para logs gerais, 30 dias para erros
```

## Benefícios

### 🔍 **Debugging Avançado**
- Rastreamento completo de requests
- Identificação rápida de gargalos
- Análise de falhas em tempo real

### 📈 **Otimização de Performance**
- Métricas detalhadas de cada componente
- Identificação de operações lentas
- Monitoramento de recursos

### 💰 **Controle de Custos**
- Tracking preciso de uso de LLM
- Projeções de custos
- Otimização de prompts

### 🧮 **Validação do Algoritmo TRI**
- Verificação de convergência
- Análise de precisão
- Calibração de parâmetros

### 📊 **Insights de Negócio**
- Padrões de uso dos usuários
- Taxa de conclusão de simulações
- Performance por área de conteúdo

## Próximos Passos

1. **Alertas Automáticos**: Notificações para anomalias
2. **Dashboard Web**: Interface visual para métricas
3. **Exportação de Dados**: CSV/Excel para análise externa
4. **Machine Learning**: Predição de performance baseada em logs
5. **Integração com APM**: New Relic, DataDog, etc.

## Comandos Rápidos

```bash
# Ver logs em tempo real
tail -f logs/application-$(date +%Y-%m-%d).log | jq .

# Análise rápida
node analyze-logs.js analyze | grep -E "(ANÁLISE|Total|Taxa)"

# Monitorar custos
watch -n 5 "node analyze-logs.js cost | tail -10"

# Verificar erros
grep -E '"level":"error"' logs/application-*.log | jq .
```
