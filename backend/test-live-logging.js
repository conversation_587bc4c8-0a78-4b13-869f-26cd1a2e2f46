#!/usr/bin/env node

/**
 * Teste do sistema de logging com servidor real
 * Faz chamadas HTTP para testar o logging em produção
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testLiveLogging() {
  console.log('🚀 Testando sistema de logging com servidor real...');
  console.log('═'.repeat(60));

  try {
    // 1. Testar health check
    console.log('\n1. 🏥 Testando Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log(`   ✓ Status: ${healthResponse.status}`);
    console.log(`   ✓ Resposta: ${healthResponse.data.message}`);

    // 2. Testar métricas
    console.log('\n2. 📊 Obtendo métricas iniciais...');
    const metricsResponse = await axios.get(`${BASE_URL}/api/metrics`);
    console.log(`   ✓ Status: ${metricsResponse.status}`);
    console.log(`   ✓ Simulações ativas: ${metricsResponse.data.simulations.count}`);
    console.log(`   ✓ Uptime: ${Math.round(metricsResponse.data.uptime)}s`);

    // 3. Simular algumas chamadas para gerar logs
    console.log('\n3. 🔄 Fazendo chamadas para gerar logs...');
    
    const requests = [
      { method: 'GET', url: '/api/health', description: 'Health check' },
      { method: 'GET', url: '/api/metrics', description: 'Métricas' },
      { method: 'GET', url: '/api/health', description: 'Health check 2' },
      { method: 'GET', url: '/api/metrics', description: 'Métricas 2' }
    ];

    for (const req of requests) {
      try {
        const response = await axios({
          method: req.method.toLowerCase(),
          url: `${BASE_URL}${req.url}`
        });
        console.log(`   ✓ ${req.description}: ${response.status}`);
      } catch (error) {
        console.log(`   ✗ ${req.description}: ${error.response?.status || 'ERRO'}`);
      }
      
      // Pequeno delay entre requests
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 4. Verificar métricas finais
    console.log('\n4. 📈 Verificando métricas finais...');
    const finalMetrics = await axios.get(`${BASE_URL}/api/metrics`);
    
    console.log('\n📊 MÉTRICAS FINAIS:');
    console.log(`   • LLM - Total de chamadas: ${finalMetrics.data.llm.totalCalls}`);
    console.log(`   • LLM - Tokens utilizados: ${finalMetrics.data.llm.totalTokens}`);
    console.log(`   • LLM - Custo estimado: $${finalMetrics.data.llm.totalCost.toFixed(4)}`);
    console.log(`   • Simulações ativas: ${finalMetrics.data.simulations.count}`);
    console.log(`   • Uptime do servidor: ${Math.round(finalMetrics.data.uptime)}s`);
    console.log(`   • Memória utilizada: ${Math.round(finalMetrics.data.memory.heapUsed / 1024 / 1024)}MB`);

    // 5. Testar endpoint inexistente para gerar erro
    console.log('\n5. 🚨 Testando geração de logs de erro...');
    try {
      await axios.get(`${BASE_URL}/api/endpoint-inexistente`);
    } catch (error) {
      console.log(`   ✓ Erro capturado: ${error.response?.status} - ${error.response?.statusText}`);
    }

    console.log('\n✅ TESTE CONCLUÍDO!');
    console.log('\n📁 Verifique os logs em:');
    console.log('   • logs/application-YYYY-MM-DD.log');
    console.log('   • logs/error-YYYY-MM-DD.log');
    
    console.log('\n🔧 Para análise detalhada:');
    console.log('   • node analyze-logs.js analyze');
    console.log('   • node monitor-logs.js start');

  } catch (error) {
    console.error('\n❌ ERRO NO TESTE:');
    console.error(`   Mensagem: ${error.message}`);
    console.error(`   Código: ${error.code}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 SOLUÇÃO:');
      console.log('   1. Inicie o servidor: node src/server.js');
      console.log('   2. Aguarde alguns segundos');
      console.log('   3. Execute este teste novamente');
    }
  }
}

// Função para testar logging de simulação (requer autenticação)
async function testSimulationLogging() {
  console.log('\n🎯 Testando logging de simulação...');
  
  try {
    // Nota: Este teste requer autenticação
    console.log('   ⚠️  Teste de simulação requer autenticação');
    console.log('   💡 Para testar completamente:');
    console.log('      1. Faça login no frontend');
    console.log('      2. Inicie uma simulação');
    console.log('      3. Responda algumas questões');
    console.log('      4. Verifique os logs com: node analyze-logs.js analyze');
    
  } catch (error) {
    console.error('Erro no teste de simulação:', error.message);
  }
}

// Função para demonstrar análise em tempo real
async function demonstrateRealTimeAnalysis() {
  console.log('\n⏱️  DEMONSTRAÇÃO: Análise em Tempo Real');
  console.log('─'.repeat(50));

  console.log('\n1. Fazendo várias chamadas para gerar dados...');
  
  // Fazer várias chamadas rapidamente
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(
      axios.get(`${BASE_URL}/api/health`).catch(() => {})
    );
  }
  
  await Promise.all(promises);
  console.log('   ✓ 10 chamadas realizadas');

  // Aguardar um pouco
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Obter métricas
  const metrics = await axios.get(`${BASE_URL}/api/metrics`);
  console.log('\n2. 📊 Métricas capturadas:');
  console.log(`   • Requests processados: ${metrics.data.llm.totalCalls || 'N/A'}`);
  console.log(`   • Uptime: ${Math.round(metrics.data.uptime)}s`);
  console.log(`   • Memória: ${Math.round(metrics.data.memory.heapUsed / 1024 / 1024)}MB`);

  console.log('\n3. 🔍 Verificando logs estruturados...');
  console.log('   ✓ Logs salvos em formato JSON');
  console.log('   ✓ Timestamps precisos');
  console.log('   ✓ Metadata completa');

  console.log('\n✨ Sistema de logging funcionando perfeitamente!');
}

// CLI
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'basic':
      testLiveLogging();
      break;
    
    case 'simulation':
      testSimulationLogging();
      break;
    
    case 'realtime':
      demonstrateRealTimeAnalysis();
      break;
    
    default:
      console.log('🧪 NCLEX Simulator - Teste de Logging Live');
      console.log('\nComandos disponíveis:');
      console.log('  basic      - Teste básico de logging');
      console.log('  simulation - Teste de logging de simulação');
      console.log('  realtime   - Demonstração em tempo real');
      console.log('\nPré-requisito: Servidor deve estar rodando (node src/server.js)');
      console.log('\nExemplo: node test-live-logging.js basic');
  }
}

module.exports = { 
  testLiveLogging, 
  testSimulationLogging, 
  demonstrateRealTimeAnalysis 
};
