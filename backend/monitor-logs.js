#!/usr/bin/env node

/**
 * Monitor de logs em tempo real
 * Exibe métricas e estatísticas do sistema de logging
 */

const llmLogger = require('./src/utils/loggers/llmLogger');
const triLogger = require('./src/utils/loggers/triLogger');
const simulationLogger = require('./src/utils/loggers/simulationLogger');
const terminalLogger = require('./src/utils/loggers/terminalLogger');
const chalk = require('chalk');
const Table = require('cli-table3');

class LogMonitor {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.refreshRate = 5000; // 5 segundos
  }

  start() {
    if (this.isRunning) {
      console.log(chalk.yellow('Monitor já está rodando!'));
      return;
    }

    this.isRunning = true;
    console.log(chalk.green('🚀 Iniciando monitor de logs...'));
    console.log(chalk.gray(`Atualizando a cada ${this.refreshRate / 1000} segundos`));
    console.log(chalk.gray('Pressione Ctrl+C para parar\n'));

    // Primeira exibição imediata
    this.displayDashboard();

    // Configurar atualização periódica
    this.intervalId = setInterval(() => {
      this.displayDashboard();
    }, this.refreshRate);

    // Configurar handlers para parada
    process.on('SIGINT', () => this.stop());
    process.on('SIGTERM', () => this.stop());
  }

  stop() {
    if (!this.isRunning) return;

    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log(chalk.yellow('\n📊 Monitor de logs parado.'));
    process.exit(0);
  }

  displayDashboard() {
    try {
      // Limpar terminal
      console.clear();

      // Header
      console.log('📊 NCLEX Simulator - Monitor de Logs');
      console.log('═'.repeat(60));
      console.log(`Última atualização: ${new Date().toLocaleString()}`);
      console.log();

      // Métricas do sistema
      this.displaySystemMetrics();
      console.log();

      // Métricas LLM
      this.displayLLMMetrics();
      console.log();

      // Simulações ativas
      this.displayActiveSimulations();
      console.log();

      // Estatísticas TRI
      this.displayTRIStats();
      console.log();

      // Footer
      console.log('─'.repeat(60));
      console.log('Pressione Ctrl+C para parar o monitor');
    } catch (error) {
      console.error('Erro ao exibir dashboard:', error.message);
    }
  }

  displaySystemMetrics() {
    const uptime = process.uptime();
    const memory = process.memoryUsage();

    console.log('🖥️  Sistema');
    console.log(`Uptime: ${this.formatDuration(uptime * 1000)}`);
    console.log(`Memória (RSS): ${this.formatBytes(memory.rss)}`);
    console.log(`Memória (Heap): ${this.formatBytes(memory.heapUsed)}`);
    console.log(`Ambiente: ${process.env.NODE_ENV || 'development'}`);
    console.log(`PID: ${process.pid}`);
  }

  displayLLMMetrics() {
    const metrics = llmLogger.getMetrics();

    console.log('🧠 Métricas LLM');
    console.log(`Total de Chamadas: ${metrics.totalCalls}`);
    console.log(`Tokens Utilizados: ${metrics.totalTokens.toLocaleString()}`);
    console.log(`Custo Estimado: $${metrics.totalCost.toFixed(4)}`);
    console.log(`Latência Média: ${Math.round(metrics.averageLatency)}ms`);
    console.log(`Taxa de Sucesso: ${metrics.successRate.toFixed(1)}%`);
    console.log(`Erros: ${metrics.errors}`);

    // Chamadas recentes
    if (metrics.recentCalls && metrics.recentCalls.length > 0) {
      console.log('\n📋 Chamadas Recentes:');
      metrics.recentCalls.slice(-3).forEach((call, index) => {
        const status = call.success ? '✓' : '✗';
        console.log(`  ${index + 1}. ${call.requestType || 'N/A'} - ${call.model || 'N/A'} - ${Math.round(call.duration)}ms - ${call.tokensUsed} tokens - ${status}`);
      });
    }
  }

  displayActiveSimulations() {
    const activeSessions = simulationLogger.getActiveSessions();

    console.log('🎯 Simulações Ativas');

    if (activeSessions.length === 0) {
      console.log('   Nenhuma simulação ativa no momento');
      return;
    }

    activeSessions.forEach((session, index) => {
      console.log(`  ${index + 1}. ID: ${session.simulationId.substring(0, 12)}... | Usuário: ${session.userId.substring(0, 12)}... | Duração: ${this.formatDuration(session.duration)} | Questões: ${session.questionCount} | Eventos: ${session.eventsCount}`);
    });
  }

  displayTRIStats() {
    console.log('📈 Estatísticas TRI');

    // Como o triLogger não expõe métricas globais, vamos simular
    const stats = {
      activeCalculations: 0,
      averageConvergence: 0,
      totalMLECalculations: 0
    };

    console.log(`Cálculos Ativos: ${stats.activeCalculations}`);
    console.log(`Convergência Média: ${stats.averageConvergence.toFixed(2)}%`);
    console.log(`Total MLE: ${stats.totalMLECalculations}`);
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  formatBytes(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  }

  // Método para exibir logs em tempo real (streaming)
  startLogStream() {
    console.log(chalk.bold.yellow('📡 Iniciando stream de logs...'));
    console.log(chalk.gray('Logs aparecerão em tempo real abaixo:\n'));

    // Interceptar console.log para capturar logs
    const originalLog = console.log;
    console.log = (...args) => {
      const timestamp = new Date().toLocaleTimeString();
      originalLog(chalk.gray(`[${timestamp}]`), ...args);
    };

    // Configurar para mostrar apenas logs importantes
    process.env.LOG_LEVEL = 'info';
  }
}

// Função para executar análise de performance
function analyzePerformance() {
  console.log(chalk.bold.yellow('🔍 Análise de Performance'));
  console.log(chalk.gray('═'.repeat(40)));

  const llmMetrics = llmLogger.getMetrics();
  
  // Análise de LLM
  console.log(chalk.bold.magenta('\n🧠 Análise LLM:'));
  
  if (llmMetrics.totalCalls > 0) {
    const avgCost = llmMetrics.totalCost / llmMetrics.totalCalls;
    const avgTokens = llmMetrics.totalTokens / llmMetrics.totalCalls;
    
    console.log(`   • Custo médio por chamada: $${avgCost.toFixed(4)}`);
    console.log(`   • Tokens médios por chamada: ${Math.round(avgTokens)}`);
    console.log(`   • Eficiência: ${(avgTokens / llmMetrics.averageLatency * 1000).toFixed(2)} tokens/s`);
    
    if (llmMetrics.successRate < 95) {
      console.log(chalk.red(`   ⚠️  Taxa de sucesso baixa: ${llmMetrics.successRate.toFixed(1)}%`));
    }
    
    if (llmMetrics.averageLatency > 5000) {
      console.log(chalk.red(`   ⚠️  Latência alta: ${Math.round(llmMetrics.averageLatency)}ms`));
    }
  } else {
    console.log(chalk.gray('   Nenhuma chamada LLM registrada'));
  }

  // Análise de simulações
  const activeSessions = simulationLogger.getActiveSessions();
  console.log(chalk.bold.green('\n🎯 Análise de Simulações:'));
  
  if (activeSessions.length > 0) {
    const avgDuration = activeSessions.reduce((acc, s) => acc + s.duration, 0) / activeSessions.length;
    const avgQuestions = activeSessions.reduce((acc, s) => acc + s.questionCount, 0) / activeSessions.length;
    
    console.log(`   • Duração média: ${Math.round(avgDuration / 1000)}s`);
    console.log(`   • Questões médias: ${Math.round(avgQuestions * 100) / 100}`);
    console.log(`   • Sessões ativas: ${activeSessions.length}`);
  } else {
    console.log(chalk.gray('   Nenhuma simulação ativa'));
  }

  console.log();
}

// CLI
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];

  const monitor = new LogMonitor();

  switch (command) {
    case 'start':
    case 'monitor':
      monitor.start();
      break;
    
    case 'stream':
      monitor.startLogStream();
      break;
    
    case 'analyze':
    case 'performance':
      analyzePerformance();
      break;
    
    case 'metrics':
      monitor.displayDashboard();
      break;
    
    default:
      console.log(chalk.bold.cyan('📊 NCLEX Simulator - Monitor de Logs'));
      console.log('\nComandos disponíveis:');
      console.log('  start, monitor  - Iniciar monitor em tempo real');
      console.log('  stream         - Stream de logs em tempo real');
      console.log('  analyze        - Análise de performance');
      console.log('  metrics        - Exibir métricas atuais');
      console.log('\nExemplo: node monitor-logs.js start');
  }
}

module.exports = { LogMonitor, analyzePerformance };
