#!/usr/bin/env node

/**
 * Analisador de logs do NCLEX Simulator
 * Extrai insights dos logs estruturados
 */

const fs = require('fs');
const path = require('path');

class LogAnalyzer {
  constructor() {
    this.logsDir = path.join(__dirname, 'logs');
  }

  // Analisar logs de um dia específico
  analyzeDay(date = new Date().toISOString().split('T')[0]) {
    const logFile = path.join(this.logsDir, `application-${date}.log`);
    
    if (!fs.existsSync(logFile)) {
      console.log(`❌ Arquivo de log não encontrado: ${logFile}`);
      return null;
    }

    console.log(`📊 Analisando logs de ${date}`);
    console.log('═'.repeat(50));

    const logs = this.readLogFile(logFile);
    const analysis = this.performAnalysis(logs);
    
    this.displayAnalysis(analysis);
    return analysis;
  }

  // Ler arquivo de log
  readLogFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.trim().split('\n').filter(line => line.trim());
    
    return lines.map(line => {
      try {
        return JSON.parse(line);
      } catch (error) {
        console.warn(`⚠️  Linha inválida ignorada: ${line.substring(0, 100)}...`);
        return null;
      }
    }).filter(Boolean);
  }

  // Realizar análise dos logs
  performAnalysis(logs) {
    const analysis = {
      overview: {
        totalEvents: logs.length,
        timeRange: this.getTimeRange(logs),
        services: this.getServiceDistribution(logs),
        actions: this.getActionDistribution(logs)
      },
      llm: this.analyzeLLMCalls(logs),
      tri: this.analyzeTRIFlow(logs),
      simulations: this.analyzeSimulations(logs),
      performance: this.analyzePerformance(logs),
      errors: this.analyzeErrors(logs)
    };

    return analysis;
  }

  // Analisar chamadas LLM
  analyzeLLMCalls(logs) {
    const llmLogs = logs.filter(log => log.service === 'LLM');
    const calls = llmLogs.filter(log => log.action === 'LLM_CALL');
    const generations = llmLogs.filter(log => log.action === 'QUESTION_GENERATED');
    const rationales = llmLogs.filter(log => log.action === 'RATIONALE_GENERATED');

    const totalTokens = calls.reduce((sum, call) => sum + (call.tokensUsed || 0), 0);
    const totalCost = calls.reduce((sum, call) => sum + (call.estimatedCost || 0), 0);
    const avgLatency = calls.length > 0 ? 
      calls.reduce((sum, call) => sum + (call.duration || 0), 0) / calls.length : 0;

    return {
      totalCalls: calls.length,
      questionsGenerated: generations.length,
      rationalesGenerated: rationales.length,
      totalTokens,
      totalCost,
      averageLatency: Math.round(avgLatency),
      modelDistribution: this.getModelDistribution(calls),
      successRate: this.calculateSuccessRate(calls)
    };
  }

  // Analisar fluxo TRI
  analyzeTRIFlow(logs) {
    const triLogs = logs.filter(log => log.service === 'TRI');
    const simulations = triLogs.filter(log => log.action === 'SIMULATION_INIT');
    const mleCalculations = triLogs.filter(log => log.action === 'MLE_CALCULATION');
    const decisions = triLogs.filter(log => log.action === 'PASS_FAIL_DECISION');

    const avgIterations = mleCalculations.length > 0 ?
      mleCalculations.reduce((sum, calc) => sum + (calc.iterations || 0), 0) / mleCalculations.length : 0;

    const convergenceRate = mleCalculations.filter(calc => calc.convergence).length / mleCalculations.length * 100;

    return {
      simulationsInitialized: simulations.length,
      mleCalculations: mleCalculations.length,
      decisionsGenerated: decisions.length,
      averageIterations: Math.round(avgIterations * 100) / 100,
      convergenceRate: Math.round(convergenceRate * 100) / 100,
      passRate: this.calculatePassRate(decisions),
      abilityDistribution: this.getAbilityDistribution(decisions)
    };
  }

  // Analisar simulações
  analyzeSimulations(logs) {
    const simLogs = logs.filter(log => log.service === 'SIMULATION');
    const created = simLogs.filter(log => log.action === 'SIMULATION_CREATED');
    const completed = simLogs.filter(log => log.action === 'SIMULATION_COMPLETED');
    const responses = simLogs.filter(log => log.action === 'RESPONSE_SUBMITTED');

    const avgDuration = completed.length > 0 ?
      completed.reduce((sum, sim) => sum + (sim.totalDuration || 0), 0) / completed.length : 0;

    const avgQuestions = completed.length > 0 ?
      completed.reduce((sum, sim) => sum + (sim.totalQuestions || 0), 0) / completed.length : 0;

    return {
      simulationsCreated: created.length,
      simulationsCompleted: completed.length,
      completionRate: created.length > 0 ? (completed.length / created.length * 100) : 0,
      totalResponses: responses.length,
      averageDuration: Math.round(avgDuration),
      averageQuestions: Math.round(avgQuestions * 100) / 100,
      accuracyDistribution: this.getAccuracyDistribution(completed)
    };
  }

  // Analisar performance
  analyzePerformance(logs) {
    const durations = logs.filter(log => log.duration).map(log => log.duration);
    const avgDuration = durations.length > 0 ? 
      durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;

    return {
      averageOperationTime: Math.round(avgDuration),
      slowestOperations: this.getSlowestOperations(logs),
      operationFrequency: this.getOperationFrequency(logs),
      peakHours: this.getPeakHours(logs)
    };
  }

  // Analisar erros
  analyzeErrors(logs) {
    const errors = logs.filter(log => log.level === 'error');
    const warnings = logs.filter(log => log.level === 'warn');

    return {
      totalErrors: errors.length,
      totalWarnings: warnings.length,
      errorRate: logs.length > 0 ? (errors.length / logs.length * 100) : 0,
      errorTypes: this.getErrorTypes(errors),
      errorsByService: this.getErrorsByService(errors)
    };
  }

  // Métodos auxiliares
  getTimeRange(logs) {
    if (logs.length === 0) return null;
    
    const timestamps = logs.map(log => new Date(log.timestamp)).sort();
    return {
      start: timestamps[0].toISOString(),
      end: timestamps[timestamps.length - 1].toISOString(),
      duration: timestamps[timestamps.length - 1] - timestamps[0]
    };
  }

  getServiceDistribution(logs) {
    const distribution = {};
    logs.forEach(log => {
      distribution[log.service] = (distribution[log.service] || 0) + 1;
    });
    return distribution;
  }

  getActionDistribution(logs) {
    const distribution = {};
    logs.forEach(log => {
      distribution[log.action] = (distribution[log.action] || 0) + 1;
    });
    return distribution;
  }

  getModelDistribution(calls) {
    const distribution = {};
    calls.forEach(call => {
      distribution[call.model] = (distribution[call.model] || 0) + 1;
    });
    return distribution;
  }

  calculateSuccessRate(calls) {
    const successfulCalls = calls.filter(call => !call.error);
    return calls.length > 0 ? (successfulCalls.length / calls.length * 100) : 0;
  }

  calculatePassRate(decisions) {
    const passDecisions = decisions.filter(decision => decision.result === 'pass');
    return decisions.length > 0 ? (passDecisions.length / decisions.length * 100) : 0;
  }

  getAbilityDistribution(decisions) {
    const abilities = decisions.map(d => d.finalAbility).filter(a => a !== undefined);
    if (abilities.length === 0) return {};

    const sorted = abilities.sort((a, b) => a - b);
    return {
      min: sorted[0],
      max: sorted[sorted.length - 1],
      median: sorted[Math.floor(sorted.length / 2)],
      average: abilities.reduce((sum, a) => sum + a, 0) / abilities.length
    };
  }

  getAccuracyDistribution(completed) {
    const accuracies = completed.map(sim => sim.accuracy).filter(a => a !== undefined);
    if (accuracies.length === 0) return {};

    return {
      average: accuracies.reduce((sum, a) => sum + a, 0) / accuracies.length,
      min: Math.min(...accuracies),
      max: Math.max(...accuracies)
    };
  }

  getSlowestOperations(logs) {
    return logs
      .filter(log => log.duration)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5)
      .map(log => ({
        service: log.service,
        action: log.action,
        duration: log.duration,
        timestamp: log.timestamp
      }));
  }

  getOperationFrequency(logs) {
    const frequency = {};
    logs.forEach(log => {
      const key = `${log.service}:${log.action}`;
      frequency[key] = (frequency[key] || 0) + 1;
    });
    return frequency;
  }

  getPeakHours(logs) {
    const hourCounts = {};
    logs.forEach(log => {
      const hour = new Date(log.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });
    return hourCounts;
  }

  getErrorTypes(errors) {
    const types = {};
    errors.forEach(error => {
      const type = error.errorType || 'Unknown';
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }

  getErrorsByService(errors) {
    const byService = {};
    errors.forEach(error => {
      byService[error.service] = (byService[error.service] || 0) + 1;
    });
    return byService;
  }

  // Exibir análise
  displayAnalysis(analysis) {
    console.log('\n📋 RESUMO GERAL');
    console.log(`Total de eventos: ${analysis.overview.totalEvents}`);
    console.log(`Período: ${analysis.overview.timeRange?.start} - ${analysis.overview.timeRange?.end}`);
    console.log(`Duração: ${Math.round((analysis.overview.timeRange?.duration || 0) / 1000)}s`);

    console.log('\n🧠 ANÁLISE LLM');
    console.log(`Chamadas totais: ${analysis.llm.totalCalls}`);
    console.log(`Questões geradas: ${analysis.llm.questionsGenerated}`);
    console.log(`Tokens utilizados: ${analysis.llm.totalTokens.toLocaleString()}`);
    console.log(`Custo estimado: $${analysis.llm.totalCost.toFixed(4)}`);
    console.log(`Latência média: ${analysis.llm.averageLatency}ms`);
    console.log(`Taxa de sucesso: ${analysis.llm.successRate.toFixed(1)}%`);

    console.log('\n📊 ANÁLISE TRI');
    console.log(`Simulações iniciadas: ${analysis.tri.simulationsInitialized}`);
    console.log(`Cálculos MLE: ${analysis.tri.mleCalculations}`);
    console.log(`Iterações médias: ${analysis.tri.averageIterations}`);
    console.log(`Taxa de convergência: ${analysis.tri.convergenceRate}%`);
    console.log(`Taxa de aprovação: ${analysis.tri.passRate.toFixed(1)}%`);

    console.log('\n🎯 ANÁLISE DE SIMULAÇÕES');
    console.log(`Criadas: ${analysis.simulations.simulationsCreated}`);
    console.log(`Concluídas: ${analysis.simulations.simulationsCompleted}`);
    console.log(`Taxa de conclusão: ${analysis.simulations.completionRate.toFixed(1)}%`);
    console.log(`Respostas totais: ${analysis.simulations.totalResponses}`);
    console.log(`Duração média: ${Math.round(analysis.simulations.averageDuration / 1000)}s`);
    console.log(`Questões médias: ${analysis.simulations.averageQuestions}`);

    console.log('\n⚡ ANÁLISE DE PERFORMANCE');
    console.log(`Tempo médio de operação: ${analysis.performance.averageOperationTime}ms`);
    
    if (analysis.performance.slowestOperations.length > 0) {
      console.log('\nOperações mais lentas:');
      analysis.performance.slowestOperations.forEach((op, i) => {
        console.log(`  ${i + 1}. ${op.service}:${op.action} - ${op.duration}ms`);
      });
    }

    console.log('\n🚨 ANÁLISE DE ERROS');
    console.log(`Total de erros: ${analysis.errors.totalErrors}`);
    console.log(`Total de warnings: ${analysis.errors.totalWarnings}`);
    console.log(`Taxa de erro: ${analysis.errors.errorRate.toFixed(2)}%`);

    if (Object.keys(analysis.errors.errorTypes).length > 0) {
      console.log('\nTipos de erro:');
      Object.entries(analysis.errors.errorTypes).forEach(([type, count]) => {
        console.log(`  • ${type}: ${count}`);
      });
    }

    if (Object.keys(analysis.errors.errorsByService).length > 0) {
      console.log('\nErros por serviço:');
      Object.entries(analysis.errors.errorsByService).forEach(([service, count]) => {
        console.log(`  • ${service}: ${count}`);
      });
    }
  }

  // Gerar relatório de custos LLM
  generateCostReport(date = new Date().toISOString().split('T')[0]) {
    const analysis = this.analyzeDay(date);
    if (!analysis) return;

    console.log('\n💰 RELATÓRIO DE CUSTOS LLM');
    console.log('═'.repeat(40));
    
    const llm = analysis.llm;
    const costPerCall = llm.totalCalls > 0 ? llm.totalCost / llm.totalCalls : 0;
    const tokensPerCall = llm.totalCalls > 0 ? llm.totalTokens / llm.totalCalls : 0;
    
    console.log(`Custo total: $${llm.totalCost.toFixed(4)}`);
    console.log(`Custo por chamada: $${costPerCall.toFixed(4)}`);
    console.log(`Tokens por chamada: ${Math.round(tokensPerCall)}`);
    console.log(`Eficiência: ${(tokensPerCall / llm.averageLatency * 1000).toFixed(2)} tokens/s`);

    // Projeções
    const dailyCost = llm.totalCost;
    const monthlyCost = dailyCost * 30;
    const yearlyCost = dailyCost * 365;

    console.log('\n📈 Projeções (baseado no uso atual):');
    console.log(`Custo mensal estimado: $${monthlyCost.toFixed(2)}`);
    console.log(`Custo anual estimado: $${yearlyCost.toFixed(2)}`);

    if (Object.keys(llm.modelDistribution).length > 0) {
      console.log('\n🤖 Uso por modelo:');
      Object.entries(llm.modelDistribution).forEach(([model, count]) => {
        const percentage = (count / llm.totalCalls * 100).toFixed(1);
        console.log(`  • ${model}: ${count} chamadas (${percentage}%)`);
      });
    }
  }

  // Gerar relatório de performance TRI
  generateTRIReport(date = new Date().toISOString().split('T')[0]) {
    const analysis = this.analyzeDay(date);
    if (!analysis) return;

    console.log('\n🧮 RELATÓRIO DE PERFORMANCE TRI');
    console.log('═'.repeat(45));
    
    const tri = analysis.tri;
    
    console.log(`Simulações processadas: ${tri.simulationsInitialized}`);
    console.log(`Cálculos MLE realizados: ${tri.mleCalculations}`);
    console.log(`Iterações médias para convergência: ${tri.averageIterations}`);
    console.log(`Taxa de convergência: ${tri.convergenceRate}%`);
    console.log(`Taxa de aprovação: ${tri.passRate}%`);

    if (tri.abilityDistribution.average !== undefined) {
      console.log('\n📊 Distribuição de Habilidades:');
      console.log(`  • Média: ${tri.abilityDistribution.average.toFixed(3)}`);
      console.log(`  • Mediana: ${tri.abilityDistribution.median.toFixed(3)}`);
      console.log(`  • Mínima: ${tri.abilityDistribution.min.toFixed(3)}`);
      console.log(`  • Máxima: ${tri.abilityDistribution.max.toFixed(3)}`);
    }

    // Recomendações
    console.log('\n💡 Recomendações:');
    if (tri.convergenceRate < 90) {
      console.log('  ⚠️  Taxa de convergência baixa - considere ajustar parâmetros MLE');
    }
    if (tri.averageIterations > 15) {
      console.log('  ⚠️  Muitas iterações para convergência - otimizar algoritmo');
    }
    if (tri.passRate < 30 || tri.passRate > 80) {
      console.log('  ⚠️  Taxa de aprovação fora do esperado - revisar threshold');
    }
  }

  // Listar arquivos de log disponíveis
  listAvailableLogs() {
    if (!fs.existsSync(this.logsDir)) {
      console.log('❌ Diretório de logs não encontrado');
      return [];
    }

    const files = fs.readdirSync(this.logsDir)
      .filter(file => file.startsWith('application-') && file.endsWith('.log'))
      .sort()
      .reverse();

    console.log('📁 Arquivos de log disponíveis:');
    files.forEach((file, index) => {
      const filePath = path.join(this.logsDir, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024).toFixed(1);
      console.log(`  ${index + 1}. ${file} (${size} KB)`);
    });

    return files;
  }
}

// CLI
if (require.main === module) {
  const analyzer = new LogAnalyzer();
  const args = process.argv.slice(2);
  const command = args[0];
  const date = args[1];

  switch (command) {
    case 'analyze':
      analyzer.analyzeDay(date);
      break;
    
    case 'cost':
    case 'costs':
      analyzer.generateCostReport(date);
      break;
    
    case 'tri':
      analyzer.generateTRIReport(date);
      break;
    
    case 'list':
      analyzer.listAvailableLogs();
      break;
    
    default:
      console.log('📊 NCLEX Simulator - Analisador de Logs');
      console.log('\nComandos disponíveis:');
      console.log('  analyze [data]  - Análise completa dos logs');
      console.log('  cost [data]     - Relatório de custos LLM');
      console.log('  tri [data]      - Relatório de performance TRI');
      console.log('  list           - Listar arquivos de log disponíveis');
      console.log('\nExemplos:');
      console.log('  node analyze-logs.js analyze');
      console.log('  node analyze-logs.js cost 2025-08-13');
      console.log('  node analyze-logs.js tri');
  }
}

module.exports = LogAnalyzer;
